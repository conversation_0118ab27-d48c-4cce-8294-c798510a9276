import React from 'react';
import Switch from '@shape-construction/arch-ui/src/Switch';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { cn } from '@shape-construction/arch-ui/src/utils/classes.ts';
import { useMediaQuery } from '@shape-construction/hooks';
import { setFieldVisibility } from 'app/store/print-preferences/printPreferencesSlice';
import { Controller } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import { useIssueExportFormValues } from '../../hooks/useIssueExportForm';
import type { FormField } from '../../types.ts';
import { buildFieldSwitchName } from '../print-factory';

type SwitchableDetailProps = {
  children: React.ReactNode;
  field: FormField;
  disabled?: boolean;
  className?: string;
  onToggle?: NonNullable<React.ComponentProps<typeof Switch.Root>['onCheckedChange']>;
};

export const SwitchableRow = ({ field, disabled, children, className, onToggle }: SwitchableDetailProps) => {
  const dispatch = useDispatch();
  const { control } = useIssueExportFormValues();
  const name = buildFieldSwitchName(field);
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const visibilityKey = `${field.key}.visible`;

  const onToggleVisibility = (
    checked: boolean,
    toggleOnChangeCb: NonNullable<React.ComponentProps<typeof Switch.Root>['onCheckedChange']>
  ) => {
    dispatch(setFieldVisibility({ key: visibilityKey, value: checked }));
    toggleOnChangeCb(checked);
    if (onToggle) onToggle(checked);
  };

  return (
    <Controller
      name={name}
      control={control}
      render={(props) => (
        <li
          className={cn(
            'flex flex-row-reverse justify-between w-full md:max-w-4xl md:flex-row md:justify-start gap-x-4',
            'items-start mb-4',
            className
          )}
        >
          <div className="grow-0">
            <Switch.Root
              small={!isLargeScreen}
              checked={!disabled && (props.field.value as boolean)}
              disabled={disabled}
              onCheckedChange={(checked) => onToggleVisibility(checked, props.field.onChange)}
            >
              <Switch.Track>
                <Switch.Thumb />
              </Switch.Track>
              <Switch.Label className="sr-only">{visibilityKey}</Switch.Label>
            </Switch.Root>
          </div>
          {children}
        </li>
      )}
    />
  );
};
