import React, { type ChangeEvent, useEffect, useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import type { IconButtonProps } from '@shape-construction/arch-ui/src/Button/IconButton/IconButton';
import { MinusIcon, PlusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import * as ProgressBar from '@shape-construction/arch-ui/src/ProgressBar';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { useStableCallback } from '@shape-construction/hooks';
import './ProgressField.css';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';

export type ProgressFieldProps = {
  name: string;
  value: number | null;
  onChange: (value: number) => void;
  step?: number;
  showControls?: boolean;
  size?: IconButtonProps['size'];
  fullWidth?: boolean;
  disabled?: boolean;
};

const incrementPercentage = (value: number | undefined | null, step: number) => {
  const rounded = Math.floor((value ?? 0) / step) * step;
  return Math.min(100, Math.max(0, rounded));
};

export const ProgressField: React.FC<ProgressFieldProps> = ({
  name,
  value,
  showControls,
  fullWidth,
  onChange,
  step = 5,
  size = 'xxs',
  disabled,
}) => {
  const messages = useMessageGetter('progressField');
  const [inputValue, setInputValue] = useState<number>(value ?? 0);

  useEffect(() => {
    value !== null && setInputValue(incrementPercentage(value, step));
  }, [step, value]);

  const updateValue = (percentage: number) => {
    const newValue = incrementPercentage(percentage, step);
    setInputValue(newValue);
    onChange(newValue);
  };

  const increment = useStableCallback(() => updateValue(inputValue + step));
  const decrement = useStableCallback(() => updateValue(inputValue - step));

  const onValueChange = useStableCallback((event: ChangeEvent<HTMLInputElement>) => {
    updateValue(Number(event.target.value));
  });

  return (
    <div
      className={cn('text-center', {
        'w-full': fullWidth,
        'w-[200px]': !fullWidth,
      })}
    >
      <div className="text-gray-500">{inputValue}%</div>
      <div className="flex gap-x-1 items-center">
        <span
          className={cn({
            'invisible group-hover/activity-cell:visible': !showControls,
          })}
        >
          <IconButton
            type="button"
            color="secondary"
            variant="outlined"
            size={size}
            icon={MinusIcon}
            onClick={decrement}
            aria-label={`Decrease ${step}%`}
            disabled={disabled}
          />
        </span>

        <div className="w-full relative">
          <div className="px-0">
            <ProgressBar.Root progress={inputValue} color="primary" size="small" disabled={disabled} />
          </div>
          <div className="absolute left-0 top-0 w-full">
            <Tooltip.Root>
              <Tooltip.Trigger asChild>
                <input
                  className={cn('progress-field-input', {
                    block: showControls,
                    'hidden group-hover/activity-cell:block': !showControls,
                    disabled,
                  })}
                  type="range"
                  name={name}
                  min="0"
                  max="100"
                  step={step}
                  value={inputValue === null ? 0 : inputValue}
                  onChange={onValueChange}
                  disabled={disabled}
                />
              </Tooltip.Trigger>
              <Tooltip.Content hidden={!disabled} side="bottom">
                {messages('disabledTooltip')}
              </Tooltip.Content>
            </Tooltip.Root>
          </div>
        </div>

        <span
          className={cn({
            'invisible group-hover/activity-cell:visible': !showControls,
          })}
        >
          <IconButton
            type="button"
            color="secondary"
            variant="outlined"
            size={size}
            icon={PlusIcon}
            onClick={increment}
            aria-label={`Add ${step}%`}
            disabled={disabled}
          />
        </span>
      </div>
    </div>
  );
};
