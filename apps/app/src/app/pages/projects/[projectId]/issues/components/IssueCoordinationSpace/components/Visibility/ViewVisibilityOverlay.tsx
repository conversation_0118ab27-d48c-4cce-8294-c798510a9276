import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema, TeamSchema } from '@shape-construction/api/src/types';
import Modal from '@shape-construction/arch-ui/src/Modal';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { PrivateCommsTeams } from './components/PrivateCommsTeams';
import { SharedCommsTeams } from './components/SharedCommsTeams';
import type { EditVisibilityOverlayProps } from './EditVisibilityOverlay';

type ViewVisibilityOverlayProps = Omit<EditVisibilityOverlayProps, 'issueId' | 'onAddTeam' | 'projectId'> & {
  isPrivate: boolean;
  projectId: ProjectSchema['id'];
  currentTeamId: TeamSchema['id'];
};

const ViewVisibilityOverlay = ({
  open,
  isPrivate,
  projectId,
  currentTeamId,
  onClose,
  involvedTeams,
  visibilityStatus,
}: ViewVisibilityOverlayProps) => {
  const messages = useMessageGetter('issue.detail.visibility');

  return (
    <Modal.Root open={open} onClose={onClose}>
      <Modal.Header bottomBorder onClose={onClose}>
        <Modal.Title>{messages('title')}</Modal.Title>
        <Modal.SubTitle>
          {isPrivate ? messages('view.privateComms.subtitle') : messages('view.sharedComms.subtitle')}
        </Modal.SubTitle>
      </Modal.Header>
      <Modal.Content className="px-0">
        <div className={cn('flex h-full flex-col gap-y-3 overflow-y-auto')}>
          {isPrivate && <PrivateCommsTeams projectId={projectId} currentTeamId={currentTeamId} avatarSize="lg" />}
          {!isPrivate && (
            <SharedCommsTeams
              visibilityStatus={visibilityStatus}
              projectId={projectId}
              currentTeamId={currentTeamId}
              involvedTeams={involvedTeams}
              avatarSize="lg"
            />
          )}
        </div>
      </Modal.Content>
    </Modal.Root>
  );
};

export default ViewVisibilityOverlay;
