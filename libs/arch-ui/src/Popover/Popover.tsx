import React, { forwardRef } from 'react';
import { useMediaQuery } from '@shape-construction/hooks';
import { Popover as PopoverPrimitive, Slot } from 'radix-ui';
import { IconButton } from '../Button';
import { XMarkIcon } from '../Icons/solid';
import { breakpoints } from '../utils/breakpoints';
import { cn } from '../utils/classes';
import { arrowClasses, type Color, closeButtonColor, contentClasses } from './config';

export const PopoverTrigger = PopoverPrimitive.Trigger;
PopoverTrigger.displayName = PopoverPrimitive.Trigger.displayName;

export const PopoverRoot = PopoverPrimitive.Root;
PopoverRoot.displayName = PopoverPrimitive.Root.displayName;

export type PopoverContentProps = React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content> & {
  /**
   * Color of the popover
   * @default 'default'
   */
  color?: Color;

  /**
   * Called when the close button is clicked on the popover
   * If not specified, doesn't show the "x" close button
   */
  onClose?: () => void;

  /** Hides the popover arrow if true
   * @default false
   */
  hideArrow?: boolean;

  /** Hides the close icon if true when onClose is provided
   * @default false
   */
  hideCloseIcon?: boolean;
};

export const PopoverContent = forwardRef<React.ComponentRef<typeof PopoverPrimitive.Content>, PopoverContentProps>(
  (
    { className, color = 'default', children, onClose, hideArrow = false, hideCloseIcon = false, asChild, ...props },
    ref
  ) => {
    const isLargeScreen = useMediaQuery(breakpoints.up('md'));

    return (
      <PopoverPrimitive.Portal>
        <div className={cn({ 'radix-popper-wrapper:transform-none! radix-popper-wrapper:inset-0!': !isLargeScreen })}>
          <div className="z-popover fixed inset-0 bg-black/50 md:hidden" />
          <PopoverPrimitive.Content
            ref={ref}
            sideOffset={8}
            onPointerDownOutside={onClose}
            onEscapeKeyDown={onClose}
            onOpenAutoFocus={(e: Event) => e.preventDefault()}
            onCloseAutoFocus={(e: Event) => e.preventDefault()}
            aria-label="dialog"
            aria-labelledby="popover-heading popover-body"
            className={cn(
              'z-popover min-w-52 p-3',
              'fixed bottom-0 w-screen',
              'md:relative md:max-w-[320px] max-md:w-screen md:bottom-auto',
              'animate-in slide-in-from-bottom md:animate-none',
              {
                'rounded-md rounded-b-none shadow-lg text-xs leading-4 font-normal md:rounded-lg md:shadow-lg':
                  !asChild,
              },
              contentClasses[color],
              className
            )}
            {...props}
          >
            {asChild ? (
              <Slot.Root>{children}</Slot.Root>
            ) : (
              <div className="overflow-y-auto text-md max-h-[inherit] p-1">
                {onClose && !hideCloseIcon && (
                  <div className="absolute top-0.5 right-0.5 cursor-pointer opacity-50">
                    <PopoverPrimitive.Close asChild>
                      <IconButton
                        autoFocus={false}
                        aria-label="popover-close-icon"
                        size="md"
                        color={closeButtonColor[color]}
                        icon={XMarkIcon}
                        onClick={onClose}
                        variant="text"
                      />
                    </PopoverPrimitive.Close>
                  </div>
                )}
                {children}
              </div>
            )}
            {isLargeScreen && !hideArrow && (
              <PopoverPrimitive.Arrow className={cn('w-3 h-2 fill-current', arrowClasses[color])} />
            )}
          </PopoverPrimitive.Content>
        </div>
      </PopoverPrimitive.Portal>
    );
  }
);
PopoverContent.displayName = PopoverPrimitive.Content.displayName;

export const PopoverContentBody: React.FC<React.ComponentProps<'p'>> = ({ children, className, ...props }) => {
  return (
    <p className={cn('font-normal text-sm max-h-96 overflow-y-auto', className)} id="popover-body" {...props}>
      {children}
    </p>
  );
};
PopoverContentBody.displayName = 'Popover.Content.Body';

export const PopoverContentFooter: React.FC<React.ComponentProps<'div'>> = ({ className, children }) => {
  return <div className={cn('pt-4', className)}>{children}</div>;
};
PopoverContentFooter.displayName = 'Popover.Content.Footer';

export const PopoverContentHeading: React.FC<React.ComponentProps<'h1'>> = ({ className, children }) => {
  return (
    <h1 className={cn('font-medium text-base pb-1', className)} id="popover-heading">
      {children}
    </h1>
  );
};
PopoverContentHeading.displayName = 'Popover.Content.Heading';
