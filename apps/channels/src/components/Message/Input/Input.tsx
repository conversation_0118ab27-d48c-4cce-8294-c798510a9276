import React, { useCallback } from 'react';
import { useActionSheet } from '@expo/react-native-action-sheet';
import { cn } from '@shape-construction/arch-ui-native';
import { CameraIcon } from '@shape-construction/arch-ui-native/src/Icons/outline';
import { PaperAirplaneIcon, PlusIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { Linking, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useAttachmentPickerActions } from 'src/components/AttachmentPicker/hooks/useAttachmentPickerActions';
import { environment } from 'src/config/environment';
import { AutoCompleteInput, type MessageInputProps, useChannelContext, useMessageInputContext } from 'stream-chat-expo';

export const Input: React.FC<MessageInputProps> = () => {
  const router = useRouter();
  const { t } = useTranslation();
  const { channel } = useChannelContext();
  const { sendMessage, isValidMessage, toggleAttachmentPicker, text } = useMessageInputContext();
  const { showActionSheetWithOptions } = useActionSheet();
  const { bottom: bottomInset } = useSafeAreaInsets();
  const { camera, gallery, documents } = useAttachmentPickerActions();
  const isInputFilled = text.length > 0;
  const projectId = channel.data?.shape_project_id;

  const onRedirectToQuickIssue = useCallback(() => {
    const url = `${environment.SHAPE_APP_URL}/projects/${projectId}/issues/quick-capture?source=channels&channelId=${channel.cid}`;

    Linking.openURL(url).catch((err) => console.error('Failed to open URL:', err));
  }, [channel, projectId]);

  const goToUploadPreview = useCallback(() => {
    router.push({
      pathname: '/channel/[channelId]/upload-preview',
      params: { channelId: channel.cid },
    });
  }, [channel.cid]);

  const takeAndUploadImage = useCallback(async () => {
    const photo = await camera();
    if (!photo?.canceled) goToUploadPreview();
  }, []);

  const toggleMediaAttachment = () => {
    const hasProjectId = Boolean(projectId);

    const options = [
      t('channel.mediaOptions.photos'),
      t('channel.mediaOptions.camera'),
      t('channel.mediaOptions.documents'),
    ];

    if (hasProjectId) {
      options.push(t('channel.actions.quickIssue'));
    }

    options.push(t('actions.cancel'));

    const cancelButtonIndex = hasProjectId ? 4 : 3;
    const destructiveButtonIndex = cancelButtonIndex;

    showActionSheetWithOptions(
      {
        cancelButtonIndex,
        destructiveButtonIndex,
        options,
        containerStyle: { paddingBottom: bottomInset },
      },
      async (buttonIndex) => {
        switch (buttonIndex) {
          case 0: {
            const mediaPickerResult = await gallery();
            if (!mediaPickerResult?.canceled) goToUploadPreview();
            break;
          }
          case 1: {
            takeAndUploadImage();
            break;
          }
          case 2: {
            const filePickerResult = await documents();
            if (!filePickerResult?.canceled) goToUploadPreview();
            break;
          }
          case 3: {
            if (hasProjectId) {
              onRedirectToQuickIssue();
            }
            break;
          }
          default:
            break;
        }
      }
    );
  };

  return (
    <View className="bg-white flex-row gap-1 justify-center items-center">
      <View className="flex justify-end">
        <TouchableOpacity aria-label="attachment options" accessibilityRole="button" onPress={toggleMediaAttachment}>
          <PlusIcon className="text-indigo-500 h-8 w-8" />
        </TouchableOpacity>
      </View>

      <View
        className={cn('flex-row items-center border flex-1 rounded-3xl border-gray-400 p-2', {
          'border-indigo-400': isValidMessage(),
        })}
      >
        <AutoCompleteInput
          additionalTextInputProps={{
            className: 'flex-1 text-base leading-5 font-normal mx-2',
          }}
        />
      </View>
      <View className="justify-end ml-2">
        {isInputFilled ? (
          <TouchableOpacity
            accessibilityRole="button"
            aria-label="send message"
            onPress={() => sendMessage()}
            className="h-9 w-9 bg-indigo-500 rounded-full items-center justify-center"
          >
            <PaperAirplaneIcon className="text-white h-4 w-4" />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            accessibilityRole="button"
            aria-label="camera"
            onPress={takeAndUploadImage}
            className="h-9 w-9"
          >
            <CameraIcon className="text-brand-subtle mt-0.5 h-7 w-7" />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};
