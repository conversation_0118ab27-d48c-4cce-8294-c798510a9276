import React from 'react';
import { useMessage } from '@messageformat/react';
import type { ShiftActivitySchema } from '@shape-construction/api/src/types';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useFeatureFlag } from '@shape-construction/feature-flags';

export type ActivityPercentageCardProps = {
  className?: string;
  percentage: ShiftActivitySchema['percentageCompleted'];
};

const ActivityPercentageCard: React.FC<ActivityPercentageCardProps> = ({ className, percentage }) => {
  const { value: isStreamliningProgressLogs } = useFeatureFlag('streamlining-progress-logs');
  const progressMessage = useMessage('weeklyPlanner.workPlans.progressTracker.drawer.activityProgress', {
    streamlining: isStreamliningProgressLogs,
    percent: percentage ?? 0,
  });

  return (
    <div className={cn('flex items-center gap-1.5 overflow-hidden text-ellipsis whitespace-nowrap', className)}>
      <span className="text-xs font-medium leading-4 text-gray-500">{progressMessage}</span>
    </div>
  );
};

export default React.memo(ActivityPercentageCard);
