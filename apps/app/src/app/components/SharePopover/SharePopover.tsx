import React, { type ReactElement } from 'react';
import { useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import { Square2StackIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import Popover from '@shape-construction/arch-ui/src/Popover';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useShare } from 'app/hooks/useShare';

type SharePopoverProps = {
  shareData: ShareData;
  title: string;
  content?: string;
  children: ReactElement;
};

export const SharePopover: React.FC<SharePopoverProps> = ({ shareData, title, content, children }) => {
  const { canNativelyShare, shareLink, copyToClipboard } = useShare(shareData.url);
  const messages = useMessageGetter('sharePopover');

  return (
    <Popover.Root>
      <Popover.Trigger asChild>{children}</Popover.Trigger>
      <Popover.Content
        hideArrow
        side="bottom"
        align="center"
        alignOffset={10}
        className={cn({ 'md:max-w-sm': !!content })}
      >
        <Popover.Content.Heading className="text-lg">{title}</Popover.Content.Heading>
        {content && <Popover.Content.Body className="flex-1 text-base">{content}</Popover.Content.Body>}
        <Popover.Content.Footer className="flex items-center space-x-3">
          {canNativelyShare && (
            <Button
              color="primary"
              variant="contained"
              size="md"
              onClick={() => shareLink(shareData.title, shareData.text)}
            >
              {messages('shareCTA')}
            </Button>
          )}
          <Button
            color="secondary"
            variant="outlined"
            size="md"
            leadingIcon={Square2StackIcon}
            onClick={() => copyToClipboard()}
          >
            {messages('copyLinkCTA')}
          </Button>
        </Popover.Content.Footer>
      </Popover.Content>
    </Popover.Root>
  );
};
