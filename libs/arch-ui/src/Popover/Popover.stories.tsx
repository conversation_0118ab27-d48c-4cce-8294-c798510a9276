import React, { type Dispatch, type SetStateAction } from 'react';
import type { Meta, StoryObj } from '@storybook/react-vite';
import Button from '../Button';
import Popover from './';
import { aligns, colors, sides } from './config';

const meta: Meta<typeof Popover.Content> = {
  title: 'Uncategorised/Popover',
  component: Popover.Content,
  decorators: [
    (Story) => (
      <div className="h-80">
        <Story />
      </div>
    ),
  ],
  argTypes: {
    align: {
      options: aligns,
      control: { type: 'select' },
    },
    side: {
      options: sides,
      control: { type: 'select' },
    },
    color: {
      options: colors,
      control: { type: 'select' },
    },
  },
};
export default meta;

type Story = StoryObj<typeof Popover.Content>;

const PopoverController: React.FC<{
  children: (opts: { setOpen: Dispatch<SetStateAction<boolean>>; open: boolean }) => React.ReactElement;
  defaultOpen?: boolean;
}> = ({ children, defaultOpen = true }) => {
  const [open, setOpen] = React.useState(defaultOpen);
  return children({ open, setOpen });
};

export const Default: Story = {
  args: {
    align: 'start',
    side: 'bottom',
    color: 'default',
  },
  render: (args) => (
    <PopoverController>
      {({ open, setOpen }) => (
        <div className="flex flex-row justify-center">
          <Popover open={open} defaultOpen>
            <Popover.Trigger>
              <Button color="primary" size="md" variant="contained" onClick={() => setOpen(!open)}>
                Toggle popover
              </Button>
            </Popover.Trigger>
            <Popover.Content align="start" side="right" onClose={() => setOpen(false)} {...args}>
              <Popover.Content.Heading>Popover</Popover.Content.Heading>
              <Popover.Content.Body>
                Lorem ipsum dolor sit amet, officia excepteur ex fugiat reprehenderit enim labore culpa sint ad nisi
                Lorem pariatur mollit ex esse exercitation amet. Nisi anim cupidatat excepteur officia. Reprehenderit
                nostrud nostrud ipsum Lorem est aliquip amet voluptate voluptate dolor minim nulla est proident.
              </Popover.Content.Body>
              <Popover.Content.Footer>
                <Button color="primary" size="sm" variant="outlined" onClick={() => setOpen(false)}>
                  Close
                </Button>
              </Popover.Content.Footer>
            </Popover.Content>
          </Popover>
        </div>
      )}
    </PopoverController>
  ),
};

export const Discovery: Story = {
  args: {
    align: 'start',
    side: 'bottom',
    color: 'discovery',
  },
  render: (args) => (
    <PopoverController>
      {({ open, setOpen }) => (
        <div className="flex flex-row justify-center">
          <Popover open={open} defaultOpen>
            <Popover.Trigger>
              <Button color="primary" size="md" variant="contained" onClick={() => setOpen(!open)}>
                Toggle popover
              </Button>
            </Popover.Trigger>
            <Popover.Content align="start" side="right" onClose={() => setOpen(false)} {...args}>
              <Popover.Content.Heading>Popover</Popover.Content.Heading>
              <Popover.Content.Body>
                Lorem ipsum dolor sit amet, officia excepteur ex fugiat reprehenderit enim labore culpa sint ad nisi
                Lorem pariatur mollit ex esse exercitation amet. Nisi anim cupidatat excepteur officia. Reprehenderit
                nostrud nostrud ipsum Lorem est aliquip amet voluptate voluptate dolor minim nulla est proident.
              </Popover.Content.Body>
              <Popover.Content.Footer>
                <Button color="discovery" size="sm" variant="outlined" onClick={() => setOpen(false)}>
                  Close
                </Button>
              </Popover.Content.Footer>
            </Popover.Content>
          </Popover>
        </div>
      )}
    </PopoverController>
  ),
};

export const WithOverflowingContent: Story = {
  args: {
    align: 'start',
    side: 'bottom',
    color: 'discovery',
  },
  render: (args) => (
    <PopoverController>
      {({ open, setOpen }) => (
        <div className="flex flex-row justify-center">
          <Popover open={open} defaultOpen>
            <Popover.Trigger>
              <Button color="primary" size="md" variant="contained" onClick={() => setOpen(!open)}>
                Toggle popover
              </Button>
            </Popover.Trigger>
            <Popover.Content align="start" side="right" onClose={() => setOpen(false)} {...args}>
              <Popover.Content.Heading>Popover</Popover.Content.Heading>
              <Popover.Content.Body>
                Lorem ipsum dolor sit amet, officia excepteur ex fugiat reprehenderit enim labore culpa sint ad nisi
                Lorem pariatur mollit ex esse exercitation amet. Nisi anim cupidatat excepteur officia. Reprehenderit
                nostrud nostrud ipsum Lorem est aliquip amet voluptate voluptate dolor minim nulla est proident. Lorem
                ipsum dolor sit amet, officia excepteur ex fugiat reprehenderit enim labore culpa sint ad nisi Lorem
                pariatur mollit ex esse exercitation amet. Nisi anim cupidatat excepteur officia. Reprehenderit nostrud
                nostrud ipsum Lorem est aliquip amet voluptate voluptate dolor minim nulla est proident. Lorem ipsum
                dolor sit amet, officia excepteur ex fugiat reprehenderit enim labore culpa sint ad nisi Lorem pariatur
                mollit ex esse exercitation amet. Nisi anim cupidatat excepteur officia. Reprehenderit nostrud nostrud
                ipsum Lorem est aliquip amet voluptate voluptate dolor minim nulla est proident. Lorem ipsum dolor sit
                amet, officia excepteur ex fugiat reprehenderit enim labore culpa sint ad nisi Lorem pariatur mollit ex
                esse exercitation amet. Nisi anim cupidatat excepteur officia. Reprehenderit nostrud nostrud ipsum Lorem
                est aliquip amet voluptate voluptate dolor minim nulla est proident. Lorem ipsum dolor sit amet, officia
                excepteur ex fugiat reprehenderit enim labore culpa sint ad nisi Lorem pariatur mollit ex esse
                exercitation amet. Nisi anim cupidatat excepteur officia. Reprehenderit nostrud nostrud ipsum Lorem est
                aliquip amet voluptate voluptate dolor minim nulla est proident.
              </Popover.Content.Body>
              <Popover.Content.Footer>
                <Button color="discovery" size="sm" variant="outlined" onClick={() => setOpen(false)}>
                  Close
                </Button>
              </Popover.Content.Footer>
            </Popover.Content>
          </Popover>
        </div>
      )}
    </PopoverController>
  ),
};
