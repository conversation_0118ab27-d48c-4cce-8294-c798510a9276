import React from 'react';
import { useMessage } from '@messageformat/react';
import type { ProjectSchema, TeamMemberSchema } from '@shape-construction/api/src/types';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import Dropdown from '@shape-construction/arch-ui/src/Dropdown';
import {
  EllipsisVerticalIcon,
  InboxArrowDownIcon,
  PencilSquareIcon,
} from '@shape-construction/arch-ui/src/Icons/outline';
import StackedListItem from '@shape-construction/arch-ui/src/StackedListItem';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useModal } from '@shape-construction/hooks';
import { useCurrentUser } from 'app/queries/users/users';
import { defaultProjectPath } from 'app/utils/defaultProjectPath';
import { Link } from 'react-router';
import { ArchiveProjectConfirmationModal } from '../../ArchiveProjectConfirmationModal/ArchiveProjectConfirmationModal';
import { EditProjectModal } from '../../components/EditProjectModal';

export interface ProjectListItemProps {
  project: ProjectSchema;
  teamMembers: TeamMemberSchema['user'][];
}

export const ProjectListItem: React.FC<ProjectListItemProps> = ({ project, teamMembers }) => {
  const {
    open: isEditProjectModalOpen,
    openModal: openEditProjectModal,
    closeModal: closeEditProjectModal,
  } = useModal(false);
  const {
    open: isArchiveProjectModalOpen,
    openModal: openArchiveProjectModal,
    closeModal: closeArchiveProjectModal,
  } = useModal(false);

  const disableEdit = !project.availableActions.edit;
  const disableArchive = !project.availableActions.archive;

  const user = useCurrentUser();
  const product = user.defaultProduct ?? 'issues';
  const projectLink = defaultProjectPath(project.id, product);

  const membersSurroundClasses = cn('flex -space-x-1 overflow-hidden', {
    'mr-2': teamMembers?.length > 0,
  });
  const members = teamMembers?.filter((item) => item?.avatarUrl).slice(0, 4) ?? [];

  return (
    <div data-cy="current-projects-each-project">
      <StackedListItem>
        <>
          <Link to={projectLink} className="flex flex-1 flex-col gap-4 md:flex-row">
            <div
              role="listitem"
              aria-label="project item"
              className="flex w-full flex-wrap items-center gap-4 text-left"
            >
              <div className="inline-flex h-10 w-10 flex-none items-center justify-center rounded-md bg-gray-400">
                {project.logoUrl ? (
                  <img src={project.logoUrl} alt={project.title} className="h-10 w-10 rounded-md object-cover" />
                ) : (
                  <span className="text-sm leading-none text-white">{project.shortName}</span>
                )}
              </div>
              <div className="flex flex-1 flex-col text-left">
                <p className="text-sm font-medium">{project.title}</p>
                <p className="flex items-center text-sm text-gray-500">{project.timezone}</p>
              </div>
              <div className="flex flex-1 space-x-1 sm:space-x-4">
                <div className={membersSurroundClasses}>
                  {members.map((teamMember) => (
                    <img
                      key={teamMember.id}
                      className="inline-block h-6 w-6 rounded-full ring-2 ring-white"
                      src={teamMember.avatarUrl || ''}
                      alt={teamMember.name || ''}
                    />
                  ))}
                </div>
                <p className="whitespace-nowrap text-sm text-gray-500">
                  {useMessage('projects.members', { count: teamMembers?.length })}
                </p>
              </div>
            </div>
          </Link>
          <div className={cn('self-start md:self-center')}>
            <Dropdown.Root>
              <Dropdown.Trigger asChild>
                <IconButton
                  type="button"
                  variant="text"
                  color="secondary"
                  size="xs"
                  icon={EllipsisVerticalIcon}
                  aria-label="project-settings"
                />
              </Dropdown.Trigger>
              <Dropdown.Items>
                <Dropdown.Item
                  key="edit-details"
                  icon={PencilSquareIcon}
                  onClick={openEditProjectModal}
                  disabled={disableEdit}
                >
                  <Tooltip.Root>
                    <Tooltip.Trigger>{useMessage('projects.actions.editDetails')}</Tooltip.Trigger>
                    <Tooltip.Content hidden={!disableEdit} side="right">
                      {useMessage('projects.tooltips.onlyAdminCanEdit')}
                    </Tooltip.Content>
                  </Tooltip.Root>
                </Dropdown.Item>
                <Dropdown.Item
                  key="archive"
                  icon={InboxArrowDownIcon}
                  onClick={openArchiveProjectModal}
                  disabled={disableArchive}
                  color="danger"
                >
                  {useMessage('projects.actions.archive')}
                </Dropdown.Item>
              </Dropdown.Items>
            </Dropdown.Root>
          </div>
        </>
      </StackedListItem>
      <EditProjectModal project={project} isOpen={isEditProjectModalOpen} onClose={closeEditProjectModal} />
      <ArchiveProjectConfirmationModal
        isOpen={isArchiveProjectModalOpen}
        onClose={closeArchiveProjectModal}
        projectId={project.id}
      />
    </div>
  );
};
