import React, { forwardRef, useEffect, useState } from 'react';
import type { ProjectSchema, TeamMemberSchema } from '@shape-construction/api/src/types';
import { UserAvatar } from '@shape-construction/arch-ui/src/Avatar';
import { InputAdornment } from '@shape-construction/arch-ui/src/InputAdornment';
import type { SelectRootProps } from '@shape-construction/arch-ui/src/Select';
import * as Select from '@shape-construction/arch-ui/src/Select';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useQuery } from '@tanstack/react-query';
import { useConstructionRoles } from 'app/components/People/constructionRoles/hooks/useConstructionRoles';
import { PersonItem } from 'app/components/PersonItem/PersonItem';
import { NoSearchResults } from 'app/components/Search/NoSearchResults';
import { SearchLoading } from 'app/components/Search/SearchLoading';
import { getProjectPeopleQueryOptions } from 'app/queries/projects/people';
import { useCurrentUser } from 'app/queries/users/users';
import { useParams } from 'react-router';

type Params = { projectId: ProjectSchema['id'] };
type PeopleSelectProps = SelectRootProps<string[], true> & {
  currentUserLabel: string;
  searchPlaceholder: string;
  triggerLabel: string;
  filterByLabel: string;
};

const initialValue: string[] = [];

export const PeopleSelect = forwardRef<React.ElementRef<typeof Select.Root>, PeopleSelectProps>(
  ({ value = initialValue, onChange, triggerLabel, currentUserLabel, searchPlaceholder, filterByLabel }, ref) => {
    const currentUser = useCurrentUser();
    const { projectId } = useParams() as Params;
    const [checked, setChecked] = useState(value);
    const [personTerm, setPersonTerm] = useState<string>();
    const { constructionRoles, isLoading: isLoadingRoles } = useConstructionRoles();
    const { data: searchPeople, isLoading } = useQuery(getProjectPeopleQueryOptions(projectId, { search: personTerm }));
    const { data: selectedPeople = [] } = useQuery({
      ...getProjectPeopleQueryOptions(projectId),
      select: (data) => {
        return checked.map((personId) => data?.find(({ id }) => personId === String(id))!) || [];
      },
    });

    const handleOnChange = (values: string[]) => {
      onChange?.(values.map(String));
    };

    const handleOnRemove = (person: TeamMemberSchema) => {
      setChecked((state) => state.filter((personId) => personId !== String(person.id)));
      handleOnChange(checked.filter((personId) => personId !== String(person.id)));
    };

    const renderContent = () => {
      if (isLoading || isLoadingRoles) {
        return (
          <Select.PanelSection className="flex-1 w-full flex flex-col justify-center">
            <SearchLoading />
          </Select.PanelSection>
        );
      }

      if (searchPeople?.length === 0) {
        return (
          <Select.PanelSection className="flex-1 w-full flex flex-col items-center justify-center">
            <NoSearchResults />
          </Select.PanelSection>
        );
      }

      return searchPeople?.map((person) => (
        <Select.Option key={person.id} value={person.id} className="md:min-h-14">
          <PersonItem
            avatar={<UserAvatar user={person.user} size="md" />}
            primaryLabel={person.user.name}
            secondaryLabel={person.team.displayName || ''}
            inlineLabel={person.user.id === currentUser?.id ? currentUserLabel : undefined}
            inlineBadge={person.constructionRole ? constructionRoles[person.constructionRole].label : null}
          />
        </Select.Option>
      ));
    };

    useEffect(() => {
      if (value) setChecked(value);
    }, [value]);

    return (
      <Select.Root
        ref={ref}
        multiple
        by={(a, b) => String(a) === String(b)}
        value={checked}
        onChange={handleOnChange}
        onClose={() => setPersonTerm('')}
      >
        <Select.Trigger
          as="div"
          variant="bordered"
          size="sm"
          startAdornment={<InputAdornment>{triggerLabel}</InputAdornment>}
        >
          <div className="text-right">
            <Select.MultipleValue value={value} condensed />
          </div>
        </Select.Trigger>

        <Select.ResponsivePanel className="md:min-w-96 md:h-[40vh] h-[80vh]">
          <Select.PanelSection className="px-4 py-5 md:hidden">
            <h1 className="text-base leading-6 font-medium">{filterByLabel}</h1>
          </Select.PanelSection>

          <Select.PanelSection className={cn('max-h-[60px] overflow-y-auto ', checked.length === 0 && 'hidden')}>
            <Select.MultipleValue
              value={selectedPeople}
              onRemove={(person) => handleOnRemove(person)}
              onRemoveAll={() => {
                setChecked([]);
                handleOnChange([]);
              }}
              displayValue={(person) => person.user.name}
            />
          </Select.PanelSection>

          <Select.PanelSection>
            <Select.Search
              placeholder={searchPlaceholder}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                const { value } = event.target;
                if (!value || value.length > 1) {
                  setPersonTerm(value);
                }
              }}
            />
          </Select.PanelSection>
          <Select.Options className="flex-1">{renderContent()}</Select.Options>
        </Select.ResponsivePanel>
      </Select.Root>
    );
  }
);

PeopleSelect.displayName = 'PeopleSelect';
