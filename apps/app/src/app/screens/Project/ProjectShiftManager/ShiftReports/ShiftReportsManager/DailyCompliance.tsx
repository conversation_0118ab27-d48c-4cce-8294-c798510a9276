import React, { useMemo } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type {
  ProjectSchema,
  ShiftReportCompletionListSchema,
  ShiftReportCompletionSchema,
} from '@shape-construction/api/src/types';
import AvatarGroupStacked from '@shape-construction/arch-ui/src/Avatar/AvatarGroupStacked';
import Card from '@shape-construction/arch-ui/src/Card';
import Popover from '@shape-construction/arch-ui/src/Popover';
import type { User as AvatarUserType } from '@shape-construction/arch-ui/src/types/User';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { formatISODateToWeekDayMonthString, getTodayISODate } from '@shape-construction/utils/DateTime';
import { useSuspenseQuery } from '@tanstack/react-query';
import { getProjectPeopleQueryOptions } from 'app/queries/projects/people';
import { useProject } from 'app/queries/projects/projects';
import { useShiftReportsCompletions } from 'app/queries/shiftReports/shiftReports';
import { useParams } from 'react-router';
import { CalendarDayReportsList } from './components/CalendarDayReportsList';
import { ReportsCalendarDay } from './components/ReportsCalendarDay';
import { ReportsCalendarGrid } from './components/ReportsCalendarGrid';

type Params = {
  projectId: ProjectSchema['id'];
};

const getShiftReportCompletionsAvatarUsers = (avatarUsers: AvatarUserType[], reports: ShiftReportCompletionSchema[]) =>
  avatarUsers.filter((avatarUser) => reports.find((report) => report.authorNewId === avatarUser.id));

const orderShiftReportsCompletions = (completions: ShiftReportCompletionListSchema) =>
  completions?.sort((completionA, completionB) => {
    const reportDateA = new Date(completionA.reportDate);
    const reportDateB = new Date(completionB.reportDate);

    return reportDateA.getTime() - reportDateB.getTime();
  });

export const DailyCompliance = () => {
  const { projectId } = useParams<Params>() as Params;
  const { data: project, isLoading: isLoadingProject } = useProject(projectId);
  const { data: shiftReportCompletions, isLoading: isLoadingCompletions } = useShiftReportsCompletions(projectId);
  const managerViewMessages = useMessageGetter('shiftReport.managerView');
  const { data: projectMembers } = useSuspenseQuery({
    ...getProjectPeopleQueryOptions(projectId),
    select: (data) =>
      data.map(({ user, newId }) => ({
        id: newId,
        name: user.name,
        avatarUrl: user.avatarUrl || undefined,
        firstName: user.firstName || undefined,
        lastName: user.lastName || undefined,
      })),
  });

  const orderedShiftReportCompletions = useMemo(
    () => orderShiftReportsCompletions(shiftReportCompletions || []),
    [shiftReportCompletions]
  );

  if (isLoadingCompletions || isLoadingProject) return <div>Loading...</div>;
  if (!project) return null;

  const todaISODate = getTodayISODate(project.timezone);

  return (
    <div className="space-y-3">
      <div className="text-base leading-6 font-semibold text-gray-800">
        {managerViewMessages('dailyComplianceTitle')}
      </div>
      <Card size="small">
        <Card.Body>
          <ReportsCalendarGrid reportCompletions={orderedShiftReportCompletions}>
            {({ completion, isLastInColumn, isLastInRow }) => {
              const completionAvatarUsers = getShiftReportCompletionsAvatarUsers(
                projectMembers!,
                completion.shiftReports
              );

              const isToday = todaISODate === completion.reportDate;

              return (
                <Popover.Root>
                  <Popover.Trigger>
                    <ReportsCalendarDay
                      highlight={isToday}
                      isLastInColumn={isLastInColumn}
                      isLastInRow={isLastInRow}
                      key={completion.reportDate}
                    >
                      <div className="flex items-start flex-col p-3 space-y-3">
                        <div
                          className={cn('text-xs leading-none font-normal', {
                            'text-brand-subtle': isToday,
                            'text-gray-500': !isToday,
                          })}
                        >
                          {isToday
                            ? managerViewMessages('today')
                            : formatISODateToWeekDayMonthString(completion.reportDate)}
                        </div>
                        <div className="text-2xl leading-7 font-bold text-gray-700">
                          {completion.shiftReports.length}
                        </div>
                        <div className="h-6">
                          <AvatarGroupStacked users={completionAvatarUsers} showMore />
                        </div>
                      </div>
                    </ReportsCalendarDay>
                  </Popover.Trigger>
                  <Popover.Content
                    hideArrow
                    side="bottom"
                    align="start"
                    sideOffset={4}
                    className="md:w-70 md:p-0 [&>div]:flex-1 [&>div]:p-0"
                  >
                    <CalendarDayReportsList
                      project={project}
                      date={completion.reportDate}
                      reports={completion.shiftReports}
                      completionAvatarUsers={completionAvatarUsers}
                    />
                  </Popover.Content>
                </Popover.Root>
              );
            }}
          </ReportsCalendarGrid>
        </Card.Body>
      </Card>
    </div>
  );
};

export { DailyCompliance as Component };
