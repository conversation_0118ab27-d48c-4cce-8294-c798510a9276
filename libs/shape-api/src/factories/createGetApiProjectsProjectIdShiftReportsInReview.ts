// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type {
  GetApiProjectsProjectIdShiftReportsInReviewPathParamsSchema,
  GetApiProjectsProjectIdShiftReportsInReviewQueryParamsSchema,
  GetApiProjectsProjectIdShiftReportsInReviewQueryResponseSchema,
} from '../types/getApiProjectsProjectIdShiftReportsInReviewSchema';
import { createAuthenticationError } from './createAuthenticationError';
import { createError } from './createError';
import { createShiftReportList } from './createShiftReportList';
import { faker } from '@faker-js/faker';

export function createGetApiProjectsProjectIdShiftReportsInReviewPathParams(
  data?: Partial<GetApiProjectsProjectIdShiftReportsInReviewPathParamsSchema>
): GetApiProjectsProjectIdShiftReportsInReviewPathParamsSchema {
  faker.seed([100]);
  return {
    ...{ project_id: faker.string.uuid() },
    ...(data || {}),
  };
}

export function createGetApiProjectsProjectIdShiftReportsInReviewQueryParams(
  data?: Partial<GetApiProjectsProjectIdShiftReportsInReviewQueryParamsSchema>
): GetApiProjectsProjectIdShiftReportsInReviewQueryParamsSchema {
  faker.seed([100]);
  return {
    ...{ page_size: faker.number.int(), after: faker.string.alpha(), before: faker.string.alpha() },
    ...(data || {}),
  };
}

/**
 * @description List of in_review shift reports
 */
export function createGetApiProjectsProjectIdShiftReportsInReview200() {
  faker.seed([100]);
  return createShiftReportList();
}

/**
 * @description Bad request
 */
export function createGetApiProjectsProjectIdShiftReportsInReview400() {
  faker.seed([100]);
  return createError();
}

/**
 * @description Authentication required
 */
export function createGetApiProjectsProjectIdShiftReportsInReview401() {
  faker.seed([100]);
  return createAuthenticationError();
}

/**
 * @description Not authorised
 */
export function createGetApiProjectsProjectIdShiftReportsInReview403() {
  faker.seed([100]);
  return createError();
}

/**
 * @description Not found
 */
export function createGetApiProjectsProjectIdShiftReportsInReview404() {
  faker.seed([100]);
  return undefined;
}

export function createGetApiProjectsProjectIdShiftReportsInReviewQueryResponse(
  data?: Partial<GetApiProjectsProjectIdShiftReportsInReviewQueryResponseSchema>
): GetApiProjectsProjectIdShiftReportsInReviewQueryResponseSchema {
  faker.seed([100]);
  return data || faker.helpers.arrayElement<any>([createGetApiProjectsProjectIdShiftReportsInReview200()]);
}
