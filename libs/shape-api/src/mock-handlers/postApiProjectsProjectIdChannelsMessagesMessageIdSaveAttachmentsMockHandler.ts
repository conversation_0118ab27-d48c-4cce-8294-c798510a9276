/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachmentsMutationResponseSchema } from '../types/postApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachmentsSchema';
import { createPostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachmentsMutationResponse } from '../factories/createPostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachments';
import { http } from 'msw';

export function postApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachmentsMockHandler(
  data?:
    | PostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachmentsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('/api/projects/:project_id/channels/messages/:message_id/save_attachments', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(
        data || createPostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachmentsMutationResponse(data)
      ),
      {
        status: 202,
      }
    );
  });
}
