import { render, screen } from 'tests/test-utils';
import { ScoringExplanationListItem } from './ScoringExplanationListItem';

describe('<ScoringExplanationListItem />', () => {
  it('renders correct text and value', () => {
    render(<ScoringExplanationListItem text="Test text" value="10%" />);

    expect(screen.getByText('Test text')).toBeInTheDocument();
    expect(screen.getByText('10%')).toBeInTheDocument();
  });
});
