import React from 'react';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { type OnboardingStep, Step } from 'app/pages/onboarding/types';

export const StepImage: React.FC<{ step?: OnboardingStep }> = React.memo(({ step }) => {
  const stepOffsetClass: string = {
    [Step.aboutYou]: '',
    [Step.joinerAboutYou]: '',
    [Step.joinerObjectives]: '',
    [Step.objectives]: '-translate-x-full',
    [Step.askCreateProject]: 'translate-x-[-200%]',
    [Step.createProject]: 'translate-x-[-300%]',
    [Step.setupTeam]: 'translate-x-[-400%]',
  }[step ?? Step.aboutYou];

  return (
    <img
      className={cn(
        'select-none pointer-events-none object-contain w-full h-full origin-left scale-[5]',
        'transition-transform duration-500 ease-in-out',
        stepOffsetClass
      )}
      src="/images/onboarding/onboarding_full.png"
      alt=""
    />
  );
});
