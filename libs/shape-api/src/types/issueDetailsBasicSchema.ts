/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueCategorySchema } from './issueCategorySchema';
import type { IssueImpactSchema } from './issueImpactSchema';
import type { IssueStateSchema } from './issueStateSchema';
import type { IssueVisibilityStatusSchema } from './issueVisibilityStatusSchema';

export type IssueDetailsBasicSchema = {
  /**
   * @type boolean
   */
  archived: boolean;
  /**
   * @type integer
   */
  assignedTeamMemberId: number | null;
  category: IssueCategorySchema | null;
  /**
   * @type string, date-time
   */
  closedAt?: string | null;
  /**
   * @type string, date-time
   */
  createdAt: string;
  /**
   * @type boolean
   */
  critical: boolean | null;
  /**
   * @type string
   */
  currentState: IssueStateSchema;
  /**
   * @type string, date-time
   */
  delayFinish: string | null;
  /**
   * @type string, date-time
   */
  delayStart: string | null;
  /**
   * @type string
   */
  description: string | null;
  /**
   * @type string, uuid
   */
  disciplineId?: string | null;
  /**
   * @type integer
   */
  draftAssigneeId: number | null;
  /**
   * @type string, date-time
   */
  dueDate: string | null;
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string
   */
  immediateAction: string | null;
  impact: IssueImpactSchema | null;
  /**
   * @type string, uuid
   */
  locationId: string | null;
  /**
   * @type integer
   */
  nextActionerId: number | null;
  /**
   * @type string | undefined, date-time
   */
  observedAt?: string;
  /**
   * @type integer
   */
  observerId: number;
  /**
   * @type integer
   */
  originatorId: number;
  /**
   * @type boolean
   */
  overdue: boolean;
  /**
   * @type string
   */
  peopleInvolvedSafety: string | null;
  /**
   * @type string, date-time
   */
  plannedClosureDate: string | null;
  /**
   * @type string
   */
  potentialImpactSeverity: string | null;
  /**
   * @type string
   */
  preventativeAction: string | null;
  /**
   * @type string, uuid
   */
  projectId: string;
  /**
   * @type integer
   */
  qualityScore: number | null;
  /**
   * @type string
   */
  referenceNumber: string | null;
  /**
   * @type boolean
   */
  safetyAlert: boolean;
  /**
   * @type integer
   */
  safetyLikelihoodScore: number | null;
  /**
   * @type string
   */
  subCategory: string | null;
  /**
   * @type string
   */
  title: string | null;
  /**
   * @type string, date-time
   */
  updatedAt: string;
  visibilityStatus: IssueVisibilityStatusSchema | null;
  /**
   * @type string
   */
  workAffected: string | null;
};
