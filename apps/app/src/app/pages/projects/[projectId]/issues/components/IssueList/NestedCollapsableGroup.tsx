import React, { use<PERSON><PERSON>back, useMemo } from 'react';
import type {
  GetApiProjectsProjectIdIssuesQueryParamsSchema,
  IssueGroupCountCollectionSchema,
  IssueGroupCountEntitySchema,
  IssueGroupCountSchema,
  ProjectSchema,
} from '@shape-construction/api/src/types';
import { ChevronDownIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import orderBy from 'lodash.orderby';
import sortBy from 'lodash.sortby';
import { useIssueGroupingDisplayKey } from '../../hooks/useIssueGroupingDisplayKey';
import { useNestedGroupItem } from '../../hooks/useNestedGroup';
import {
  type IssueGroupKey,
  type IssueGroupOption,
  type StatusFields,
  useGroupingOptions,
} from '../IssuesFilters/hooks/useGroupingOptions';

const depthClasses = {
  1: 'z-8 pl-0 top-0 h-8',
  2: 'z-7 pl-4 top-8 h-8',
  3: 'z-6 pl-8 top-16 h-8',
  4: 'z-5 pl-12 top-24 h-8',
  5: 'z-4 pl-16 top-32 h-8',
  6: 'z-3 pl-20 top-40 h-8',
  7: 'z-2 pl-24 top-48 h-8',
};

interface GroupButtonProps {
  children: React.ReactNode;
  id: string;
  defaultCollpased?: boolean;
  count: number;
  innerCount?: number;
  depth: number;
  entity?: string;
  group?: string | number;
  groupLabel?: string;
}

const GroupButton: React.FC<GroupButtonProps> = React.memo(
  ({ id, defaultCollpased, count, innerCount, depth, entity, group, groupLabel, children }) => {
    const { node, onToggleNode } = useNestedGroupItem(id, Boolean(defaultCollpased));
    const groupHeaderClassName = cn(
      'w-full flex p-1 sticky top-0 md:-top-4 cursor-pointer bg-gray-100 items-center',
      // @ts-expect-error ts-migrate(2322) FIXME: Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ 1: string; 2: string; 3: string; 4: string; 5: string; 6: string; }'.ts(7053)
      depthClasses[depth] || depthClasses[7]
    );
    const chevronClassName = cn('w-4 h-4 text-gray-400', {
      'transform -rotate-90': node.collapsed,
    });

    const nodeCount = useMemo(() => {
      if (Number.isInteger(innerCount)) return `${count} (${innerCount})`;
      return count;
    }, [count, innerCount]);

    return (
      <div data-cy="group">
        <button
          role="row"
          aria-level={depth}
          aria-expanded={!node.collapsed}
          aria-label={group as string}
          className={groupHeaderClassName}
          onClick={onToggleNode}
          type="button"
          data-cy={`group-collapse-depth-${depth}`}
          data-testid={`group-collapse-depth-${depth}`}
        >
          <ChevronDownIcon className={chevronClassName} />
          <span className="ml-2 flex flex-row items-center gap-2 text-xs font-medium text-gray-400">
            <span>{groupLabel || group}</span>
            <span className="text-gray-700">{entity}</span>
            <span>{nodeCount}</span>
          </span>
        </button>
        {!node.collapsed ? children : null}
      </div>
    );
  }
);

const applyIssueGroupingOrder = (
  data: IssueGroupCountEntitySchema[] | undefined,
  customOrdering: IssueGroupOption['customOrdering'] | undefined,
  buildDisplayKey: ReturnType<typeof useIssueGroupingDisplayKey>
) => {
  if (customOrdering) {
    return sortBy(data, (item) => {
      const identifier = item.identifier as StatusFields | null;
      return customOrdering?.indexOf(identifier ?? 'default');
    });
  }

  // Sort alphabetically and leave the null values to the end
  return orderBy(data, ({ identifier }) => identifier && buildDisplayKey(identifier));
};

const getGroupingParameters =
  (previousGrouping: GetApiProjectsProjectIdIssuesQueryParamsSchema) =>
  (
    group: IssueGroupKey,
    identifier: IssueGroupCountEntitySchema['identifier'],
    collection: IssueGroupCountCollectionSchema | undefined
  ): GetApiProjectsProjectIdIssuesQueryParamsSchema => {
    const configuration: Partial<Record<IssueGroupKey, Partial<GetApiProjectsProjectIdIssuesQueryParamsSchema>>> = {
      [group]: { [group]: identifier ?? '' },
      custom_field: {
        custom_field_id: collection?.customFieldId as string,
        custom_field_value: (identifier as string) ?? '',
      },
    };

    return {
      ...previousGrouping,
      ...configuration[group],
    };
  };

export interface NestedCollapsableGroupProps {
  projectId: ProjectSchema['id'];
  id?: string;
  depth?: number;
  issueGroup: IssueGroupCountSchema;
  isDefaultCollapsed: (Args: { depth: number; identifier: IssueGroupCountEntitySchema['identifier'] }) => boolean;
  grouping?: GetApiProjectsProjectIdIssuesQueryParamsSchema;
  children: (props: { grouping: GetApiProjectsProjectIdIssuesQueryParamsSchema }) => React.ReactNode;
}

export const NestedCollapsableGroup: React.FC<NestedCollapsableGroupProps> = React.memo(
  ({ depth = 1, id, grouping = {}, issueGroup, ...props }) => {
    const { projectId, children } = props;
    const { getOption } = useGroupingOptions();
    const currentGroup = issueGroup?.groupCollection?.group as IssueGroupKey;
    const buildDisplayKey = useIssueGroupingDisplayKey(
      projectId,
      currentGroup,
      issueGroup?.groupCollection?.groupLabel
    );
    const groupOption = getOption(currentGroup);
    const groupingParamsGetter = getGroupingParameters(grouping);
    const usedEntities = useMemo(
      () =>
        applyIssueGroupingOrder(
          issueGroup?.groupCollection?.groupEntities,
          groupOption?.customOrdering,
          buildDisplayKey
        ),
      [buildDisplayKey, groupOption?.customOrdering, issueGroup?.groupCollection?.groupEntities]
    );

    const renderNode = useCallback(
      (identifier: IssueGroupCountEntitySchema['identifier'], entity: IssueGroupCountEntitySchema, newId: string) => {
        const isLeafNode = !entity.groupCollection;
        const groupingParameters = groupingParamsGetter(currentGroup, identifier, issueGroup.groupCollection);

        if (isLeafNode) return children({ grouping: groupingParameters });

        return (
          <NestedCollapsableGroup
            depth={depth + 1}
            id={newId}
            grouping={groupingParameters}
            issueGroup={{
              groups: [],
              groupCollection: entity.groupCollection as IssueGroupCountCollectionSchema,
              totalCount: entity.totalCount,
            }}
            {...props}
          />
        );
      },
      [children, currentGroup, depth, groupingParamsGetter, issueGroup.groupCollection, props]
    );

    const renderInnerNode = useCallback(
      (entity: IssueGroupCountEntitySchema, newId: string) => (
        <React.Fragment key={entity.identifier}>
          {entity.innerEntity && renderInnerNode(entity.innerEntity, newId)}
          {
            /**
             * Since inner nodes are side by side with children,
             * we need to identify them different to avoid id collision.
             * Example: 1.1.inner.0
             * */
            renderNode(entity.identifier, entity, [newId, 'inner'].join('.'))
          }
        </React.Fragment>
      ),
      [renderNode]
    );

    return (
      <div className="flex flex-col gap-1">
        {usedEntities?.map((entity, index) => {
          const identifier = entity.identifier;
          const groupEntityName = buildDisplayKey(identifier);
          const defaultCollpased = props.isDefaultCollapsed({ depth, identifier });
          /**
           * This will generate a unique id based on the index.
           * Example: 1.1.0
           * */
          const newId = [id, `${index}`].filter(Boolean).join('.');

          return (
            <GroupButton
              id={newId}
              key={identifier}
              group={groupOption?.name}
              groupLabel={issueGroup.groupCollection.groupLabel}
              entity={groupEntityName}
              count={entity.totalCount}
              innerCount={entity.innerEntity?.totalCount}
              depth={depth}
              defaultCollpased={defaultCollpased}
            >
              {entity.innerEntity && renderInnerNode(entity.innerEntity, newId)}
              {renderNode(identifier, entity, newId)}
            </GroupButton>
          );
        })}
      </div>
    );
  }
);
