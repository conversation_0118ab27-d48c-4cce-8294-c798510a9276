import React, { useMemo } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { LocationListSchema, LocationSchema } from '@shape-construction/api/src/types';
import ListWithSeparator from '@shape-construction/arch-ui/src/ListWithSeparator';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { truncatedLocationsList } from 'app/components/Utils/locations';

const TRUNCATE_LENGTH = 2;

type LocationTextProps = {
  className?: string;
  locationId: LocationSchema['id'] | null;
  locations: LocationListSchema | undefined;
  truncate?: boolean;
};

export const LocationText: React.FC<LocationTextProps> = ({ className, locations, locationId, truncate = true }) => {
  const messages = useMessageGetter('weeklyPlanner.workPlans.planEditor.location');

  const { locationCodes, fullLocationCodes } = useMemo(() => {
    if (!locations) return {};

    const fullLocationCodes = truncatedLocationsList(locations, locationId, locations?.length);
    const locationCodes = truncate ? truncatedLocationsList(locations, locationId, TRUNCATE_LENGTH) : fullLocationCodes;

    return { locationCodes, fullLocationCodes };
  }, [locations, locationId]);

  if (!locationId || !locations) return <span className="text-gray-400 font-normal">{messages('empty')}</span>;

  return (
    <Tooltip.Root>
      <Tooltip.Trigger asChild aria-label="tooltip-trigger">
        <div>
          <ListWithSeparator className={cn('flex-nowrap', className)}>{locationCodes}</ListWithSeparator>
        </div>
      </Tooltip.Trigger>
      <Tooltip.Content side="right">{fullLocationCodes?.join(' > ')}</Tooltip.Content>
    </Tooltip.Root>
  );
};
