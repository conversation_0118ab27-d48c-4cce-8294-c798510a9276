import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { IssueSchema } from '@shape-construction/api/src/types';
import { MinusCircleIcon as MinusCircleIconOutline } from '@shape-construction/arch-ui/src/Icons/outline';
import { CheckCircleIcon, MinusCircleIcon as MinusCircleIconSolid } from '@shape-construction/arch-ui/src/Icons/solid';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { textImpactColors } from '../../../../colors';
import { getImpactRecord, type ImpactType } from '../../../../constants/Impact';

interface ImpactIconProps {
  impactValue: ImpactType;
  iconClass: string;
}

interface IssueCardImpactProps {
  className?: string;
  impact: IssueSchema['impact'];
}

const ImpactIcon = ({ impactValue, iconClass }: ImpactIconProps) => {
  const defaultIcon = <MinusCircleIconSolid className={cn('h-4 w-4', iconClass)} />;
  const icons: Partial<Record<ImpactType, React.ReactElement>> = {
    notEntered: <MinusCircleIconOutline className={cn('h-4 w-4', iconClass)} />,
    completedDelay: <CheckCircleIcon className={cn('h-4 w-4', iconClass)} />,
  };

  return icons[impactValue] ?? defaultIcon;
};

const IssueCardImpact = ({ className, impact }: IssueCardImpactProps) => {
  const messages = useMessageGetter('issue.detail');
  const impactRecord = getImpactRecord(messages);
  const impactValue = impact ?? impactRecord.notEntered.value;
  const iconClass = textImpactColors[impactValue];

  return (
    <div
      className={cn(
        textImpactColors[impactValue],
        'flex items-center gap-1.5 overflow-hidden whitespace-nowrap',
        className
      )}
    >
      <ImpactIcon impactValue={impactValue} iconClass={iconClass} />
      <span className="text-xs font-medium leading-4">{impactRecord[impactValue]?.label}</span>
    </div>
  );
};

export default React.memo(IssueCardImpact);
