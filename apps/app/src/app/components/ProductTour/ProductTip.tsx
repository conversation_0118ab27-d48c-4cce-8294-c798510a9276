import React, { type ReactElement, type ReactNode } from 'react';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { ProductTipBubble, type ProductTipBubbleProps } from 'app/components/ProductTour/ProductTipBubble';
import type { TourPage } from 'app/config/productTour.config';

export type ProductTipProps<Page extends TourPage> = ProductTipBubbleProps<Page> & {
  children: ReactNode;
  pulseSide?: 'left' | 'right';
};

export const ProductTip = <Page extends TourPage>({
  children,
  className,
  pulseSide = 'right',
  ...bubbleProps
}: ProductTipProps<Page>): ReactElement => {
  const wrapperClass = {
    left: 'flex-row-reverse',
    right: '',
  }[pulseSide];

  return (
    <div className={cn('flex', className, wrapperClass)}>
      {children}
      <ProductTipBubble {...bubbleProps} />
    </div>
  );
};
