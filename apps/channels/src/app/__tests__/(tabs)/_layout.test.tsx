import * as BottomSheet from '@shape-construction/arch-ui-native/src/BottomSheet';
import { View } from 'react-native';
import { TabsLayout } from 'src/app/(authenticated)/(tabs)/_layout';
import { environment } from 'src/config/environment';
import { renderRouter, screen } from 'src/tests/test-utils';

// Mock the environment module
jest.mock('src/config/environment', () => ({
  environment: {
    FEATURE_FLAG_CAPTURE_NOTES: false,
  },
}));

const RenderNull = () => null;

describe('<TabsLayout />', () => {
  describe('when settings button is pressed', () => {
    it('navigates to /settings', async () => {
      const { user } = renderRouter(
        {
          '/(tabs)/_layout': () => <TabsLayout />,
          '/(tabs)/index': RenderNull,
          '/(tabs)/create': RenderNull,
          '/(tabs)/settings': RenderNull,
        },
        { initialUrl: '/' }
      );

      await user.press(await screen.findByRole('button', { name: 'settings.title' }));

      expect(screen).toHavePathname('/settings');
    });
  });

  describe('when channels list button is pressed', () => {
    it('navigates to /', async () => {
      const { user } = renderRouter(
        {
          '/(tabs)/_layout': () => <TabsLayout />,
          '/(tabs)/index': RenderNull,
          '/(tabs)/create': RenderNull,
          '/(tabs)/settings': RenderNull,
        },
        { initialUrl: '/settings' }
      );

      await user.press(await screen.findByRole('button', { name: 'channelList.title' }));

      expect(screen).toHavePathname('/');
    });
  });

  describe('when FEATURE_FLAG_CAPTURE_NOTES is false', () => {
    beforeEach(() => {
      environment.FEATURE_FLAG_CAPTURE_NOTES = false;
    });

    it('renders the tabs options', async () => {
      renderRouter(
        {
          '/(tabs)/_layout': () => <TabsLayout />,
          '/(tabs)/index': RenderNull,
          '/(tabs)/create': RenderNull,
          '/(tabs)/settings': RenderNull,
        },
        { initialUrl: '/' }
      );

      expect(await screen.findByRole('button', { name: 'channelList.title' })).toBeOnTheScreen();
      expect(await screen.findByRole('button', { name: 'settings.title' })).toBeOnTheScreen();
      expect(screen.queryByRole('button', { name: 'create.title' })).not.toBeOnTheScreen();
    });

    it('renders the create button in the header', async () => {
      renderRouter(
        {
          '/(tabs)/_layout': () => <TabsLayout />,
          '/(tabs)/index': RenderNull,
          '/(tabs)/create': RenderNull,
          '/(tabs)/settings': RenderNull,
        },
        { initialUrl: '/' }
      );

      expect(await screen.findByRole('button', { name: 'create channel' })).toBeOnTheScreen();
    });

    describe('when create channel is pressed', () => {
      it('navigates to /channel/new', async () => {
        const { user } = renderRouter(
          {
            '/(tabs)/_layout': () => <TabsLayout />,
            '/(tabs)/index': RenderNull,
            '/(tabs)/create': RenderNull,
            '/(tabs)/settings': RenderNull,
          },
          { initialUrl: '/' }
        );

        await user.press(await screen.findByRole('button', { name: 'create channel' }));

        expect(screen).toHavePathname('/channel/new');
      });
    });
  });

  describe('when FEATURE_FLAG_CAPTURE_NOTES is true', () => {
    beforeEach(() => {
      environment.FEATURE_FLAG_CAPTURE_NOTES = true;
    });

    it('renders the tabs options', async () => {
      renderRouter(
        {
          '/(tabs)/_layout': () => (
            <BottomSheet.Root>
              <TabsLayout />
            </BottomSheet.Root>
          ),
          '/(tabs)/index': () => <View data-testid="tab-screen-index" />,
          '/(tabs)/create': () => <View data-testid="tab-screen-create" />,
          '/(tabs)/settings': () => <View data-testid="tab-screen-settings" />,
        },
        { initialUrl: '/' }
      );

      expect(await screen.findByRole('button', { name: 'channelList.title' })).toBeOnTheScreen();
      expect(await screen.findByRole('button', { name: 'settings.title' })).toBeOnTheScreen();
      expect(await screen.findByRole('button', { name: 'create.title' })).toBeOnTheScreen();
    });

    describe('when create menu is pressed', () => {
      it('renders the menu options', async () => {
        const { user } = renderRouter(
          {
            '/(tabs)/_layout': () => (
              <BottomSheet.Root>
                <TabsLayout />
              </BottomSheet.Root>
            ),
            '/(tabs)/index': RenderNull,
            '/(tabs)/create': RenderNull,
            '/(tabs)/settings': RenderNull,
          },
          { initialUrl: '/' }
        );

        await user.press(await screen.findByRole('button', { name: 'create.title' }));

        expect(await screen.findByRole('link', { name: 'create.channel.title' })).toBeOnTheScreen();
        expect(await screen.findByRole('link', { name: 'create.note.title' })).toBeOnTheScreen();
      });

      describe('when new channel button is pressed', () => {
        it('navigates to /channel/new', async () => {
          const { user } = renderRouter(
            {
              '/(tabs)/_layout': () => (
                <BottomSheet.Root>
                  <TabsLayout />
                </BottomSheet.Root>
              ),
              '/(tabs)/index': RenderNull,
              '/(tabs)/create': RenderNull,
              '/(tabs)/settings': RenderNull,
            },
            { initialUrl: '/' }
          );

          await user.press(await screen.findByRole('button', { name: 'create.title' }));
          await user.press(await screen.findByRole('link', { name: 'create.channel.title' }));

          expect(screen).toHavePathname('/channel/new');
        });
      });

      describe('when new notes is pressed', () => {
        it('navigates to /notes', async () => {
          const { user } = renderRouter(
            {
              '/(tabs)/_layout': () => (
                <BottomSheet.Root>
                  <TabsLayout />
                </BottomSheet.Root>
              ),
              '/(tabs)/index': RenderNull,
              '/(tabs)/create': RenderNull,
            },
            { initialUrl: '/' }
          );

          await user.press(await screen.findByRole('button', { name: 'create.title' }));
          await user.press(await screen.findByRole('link', { name: 'create.note.title' }));

          expect(screen).toHavePathname('/notes/new');
        });
      });
    });
  });
});
