import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import SplitScreen from '@shape-construction/arch-ui/src/SplitScreen';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useInstallApp } from 'app/hooks/useInstallApp';

export const RightPanel = () => {
  const messages = useMessageGetter('install');
  const { isInstallSupported, isInStandaloneMode, promptToInstall } = useInstallApp();
  const hideInstallButton = !isInstallSupported || isInStandaloneMode;

  const handleInstall = () => {
    promptToInstall();
  };

  const bgroundClasses = cn(
    "bg-[url('/images/install/install-background.png')]",
    'bg-bottom bg-contain bg-scroll lg:bg-fixed bg-repeat-x bg-indigo-500 '
  );

  const title = hideInstallButton ? messages('altTitle') : messages('installTitle');
  const description = hideInstallButton ? messages('altDescription') : messages('installDescription');

  return (
    <SplitScreen.RightPanel data-testid="right-panel" className={bgroundClasses}>
      <div className="flex flex-col h-screen m-auto items-center justify-center">
        <img
          className="lg:w-[460px] xl:w-[600px] select-none pointer-events-none"
          src="/images/install/install-foreground.png"
          alt={messages('altDescription')}
        />
        <div className="flex flex-col max-w-md items-center text-center text-white">
          <div className="text-2xl font-semibold leading-10 mt-6">{title}</div>
          <div className="text-base font-medium leading-6 mt-3 mb-6">{description}</div>
          {!hideInstallButton && (
            <Button color="primary" size="xl" variant="outlined" onClick={handleInstall}>
              {messages('installTitle')}
            </Button>
          )}
        </div>
      </div>
    </SplitScreen.RightPanel>
  );
};
