import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { IssueSchema, ProjectSchema, TeamSchema } from '@shape-construction/api/src/types';
import { GlobeAmericasIcon, LockClosedIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useMediaQuery } from '@shape-construction/hooks';
import useOverlay from 'app/hooks/useToggle';
import type { Channel } from 'app/queries/issues/feed/feed';
import { ViewVisibilityTriggerLarge } from './components/ViewVisibilityTriggerLarge';
import { ViewVisibilityTriggerSmall } from './components/ViewVisibilityTriggerSmall';
import ViewVisibilityOverlay from './ViewVisibilityOverlay';

export interface ViewVisibilityProps {
  issue: IssueSchema;
  channel: Channel;
  isPrivate?: boolean;
  projectId: ProjectSchema['id'];
  currentTeamId: TeamSchema['id'];
}

export const ViewVisibility = ({
  issue,
  channel,
  projectId,
  currentTeamId,
  isPrivate = false,
}: ViewVisibilityProps) => {
  const messages = useMessageGetter('issue.detail');
  const { involvedTeams = [], visibilityStatus } = issue;
  const [viewVisibilityOverlayOpen, closeViewVisibilityOverlay, openViewVisibilityOverlay] = useOverlay();

  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const ChannelIcon = channel === 'public' ? GlobeAmericasIcon : LockClosedIcon;

  const renderVisibilityHintSmall = () => {
    return (
      <div className={cn('flex items-center gap-x-2 mt-1.5', { 'ml-2': channel === 'team' })}>
        <ViewVisibilityTriggerSmall channel={channel} onClick={openViewVisibilityOverlay} />
        <span className="text-xs text-gray-500">
          {channel === 'public' && <>{messages('activity.input.hint.open')} </>}
          {channel === 'team' && <>{messages('activity.input.hint.team')} </>}
        </span>
      </div>
    );
  };

  const renderVisibilityHintLarge = () => {
    return (
      <div className="flex items-center gap-x-1 mt-1">
        <ChannelIcon
          className={cn('h-4 w-5 text-gray-400', {
            'ml-1': channel === 'team',
            'ml-[28px]': channel === 'public',
          })}
        />
        <span className="text-xs text-gray-500 ml-1">
          {channel === 'public' && <>{messages('activity.input.hint.open')} </>}
          {channel === 'team' && <>{messages('activity.input.hint.team')} </>}
        </span>
        <ViewVisibilityTriggerLarge onClick={openViewVisibilityOverlay}>
          {messages('visibility.details')}
        </ViewVisibilityTriggerLarge>
      </div>
    );
  };

  return (
    <>
      {isLargeScreen ? renderVisibilityHintLarge() : renderVisibilityHintSmall()}
      <ViewVisibilityOverlay
        open={viewVisibilityOverlayOpen}
        isPrivate={isPrivate}
        projectId={projectId}
        currentTeamId={currentTeamId}
        onClose={closeViewVisibilityOverlay}
        visibilityStatus={visibilityStatus}
        involvedTeams={involvedTeams}
      />
    </>
  );
};
