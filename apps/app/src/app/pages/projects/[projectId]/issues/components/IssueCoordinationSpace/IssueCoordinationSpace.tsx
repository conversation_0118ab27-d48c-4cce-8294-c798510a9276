import React, { useEffect } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { IssueSchema } from '@shape-construction/api/src/types';
import {
  DocumentTextIcon,
  GlobeEuropeAfricaIcon,
  LockClosedIcon,
  ShapeProjectGalleryIcon,
} from '@shape-construction/arch-ui/src/Icons/outline';
import Tabs from '@shape-construction/arch-ui/src/Tabs/ScrollableTabs';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useSuspenseQuery } from '@tanstack/react-query';
import {
  type FeatureLimitProps,
  FeatureLimits,
} from 'app/components/SubscriptionPlanFeatures/FeatureLimits/FeatureLimits';
import { ISSUE_STATES } from 'app/constants/IssueStates';
import { useUpdateIssue, useVisitIssue } from 'app/queries/issues/issues';
import { getProjectQueryOptions } from 'app/queries/projects/projects';
import { useCurrentUser } from 'app/queries/users/users';
import { atom, useAtom } from 'jotai';
import { useDispatch } from 'react-redux';
import { IssueStatusStatement } from './components/IssueStatusStatement/IssueStatusStatement';
import { DetailsTab } from './components/tabs/DetailsTab';
import { GalleryTab } from './components/tabs/GalleryTab';
import { OpenChatTab } from './components/tabs/OpenChatTab';
import { PrivateChatTab } from './components/tabs/PrivateChatTab';
import { SelectedIssueProvider, useSelectedIssue } from './hooks/issueContext';
import { layoutConfig } from './layoutConfig';
import { SectionBanner } from './SectionBanner';
import { SectionHeader } from './SectionHeader';
import { SectionStateActions } from './SectionStateActions';

export enum IssueCoordinationActivityTabs {
  PRIVATE_CHAT = 0,
  OPEN_CHAT = 1,
  GALLERY = 2,
  DETAILS = 3,
}

export type IssueCoordinationActivityTabsConfiguration = Array<{
  content: React.ReactElement;
  disabled?: boolean;
  icon?: React.ReactElement;
  label: string;
  subscriptionPlanFeatureName?: FeatureLimitProps['featureName'];
  updates?: number;
}>;

type IssueCoordinationSpaceProps = {
  issue: IssueSchema;
  showNavigationActions?: boolean;
};

const selectedTabAtom = atom(IssueCoordinationActivityTabs.OPEN_CHAT);

const IssueCoordinationSpacePrivate = ({ issue, showNavigationActions }: IssueCoordinationSpaceProps) => {
  const dispatch = useDispatch();
  const { mutate: updateIssue } = useUpdateIssue();
  const { mutate: visitIssue, data: issueVisitData } = useVisitIssue();
  const [, setSharedIssue] = useSelectedIssue();
  const user = useCurrentUser();
  const projectQuery = useSuspenseQuery(getProjectQueryOptions(issue.projectId));
  const project = projectQuery.data!;
  const [activeActivityTab, setActiveActivityTab] = useAtom(selectedTabAtom);

  const isDraftIssue = issue.currentState === ISSUE_STATES.DRAFT;
  const sections = isDraftIssue ? layoutConfig.draft : layoutConfig.default;
  const canEdit = issue.availableActions.edit;
  const latestStatusStatement = issue.issueStatusStatement;
  const showIssueStatusStatement = canEdit || latestStatusStatement;

  const activityTabsMessage = useMessageGetter('issue.detail.activity.tabs');

  const tabsConfiguration: IssueCoordinationActivityTabsConfiguration = [
    {
      disabled: isDraftIssue,
      label: activityTabsMessage('teamComms'),
      icon: <LockClosedIcon className="h-5 w-5" />,
      subscriptionPlanFeatureName: 'issuePrivateChat' as const,
      content: <PrivateChatTab user={user} issue={issue} project={project} />,
      updates: issueVisitData?.previouslyUnreadTeamUpdatesCount,
    },
    {
      disabled: isDraftIssue,
      label: activityTabsMessage('sharedComms'),
      icon: <GlobeEuropeAfricaIcon className="h-5 w-5" />,
      content: <OpenChatTab user={user} issue={issue} project={project} />,
      updates: issueVisitData?.previouslyUnreadPublicUpdatesCount,
    },
    {
      label: activityTabsMessage('gallery'),
      icon: <ShapeProjectGalleryIcon className="h-5 w-5" />,
      content: <GalleryTab project={project} issue={issue} />,
    },
    {
      label: activityTabsMessage('details'),
      icon: <DocumentTextIcon className="h-5 w-5" />,
      content: <DetailsTab issue={issue} project={project} />,
    },
  ];

  useEffect(() => {
    visitIssue({ projectId: issue.projectId, issueId: issue.id });
  }, [visitIssue, issue.projectId, issue.id]);

  useEffect(() => {
    // update context issue
    setSharedIssue(issue);
  }, [issue, dispatch, setSharedIssue]);

  if (!issue) return <div>Loading...</div>;

  const getSelectedTabIndex = () => {
    const defaultTab =
      issue.currentState === 'draft' ? IssueCoordinationActivityTabs.DETAILS : IssueCoordinationActivityTabs.OPEN_CHAT;

    return tabsConfiguration[activeActivityTab].disabled ? defaultTab : activeActivityTab;
  };
  const selectedTabIndex = getSelectedTabIndex();
  const selectedTab = tabsConfiguration[selectedTabIndex];

  return (
    <div className="h-screen md:h-full flex flex-col" data-cy="issue-details">
      <div className="flex-1 w-full flex flex-col max-w-4xl mx-auto">
        <SectionBanner issue={issue} />
        <SectionHeader
          issue={issue}
          showNavigationActions={showNavigationActions}
          handleSave={(values) =>
            updateIssue({ projectId: issue.projectId, issueId: issue.id, data: { issue: values } })
          }
        />

        {showIssueStatusStatement && (
          <div
            className={cn('px-4 md:px-8', {
              'mb-2.5': sections.showStateActions,
            })}
          >
            <IssueStatusStatement issue={issue} timezone={project.timezone} />
          </div>
        )}

        {sections.showStateActions && (
          <div className="px-4 md:px-8">
            <SectionStateActions issue={issue} project={project} />
          </div>
        )}

        <div className="h-full flex-1 flex flex-col overflow-hidden mt-4">
          <Tabs>
            {tabsConfiguration.map((tabConfig, tabIndex) => {
              const renderTabElement = (key?: string, disabled?: boolean) => {
                const updateCounter = tabConfig.updates || null;
                const isDisabled = tabConfig.disabled || disabled;
                const isSelected = tabIndex === selectedTabIndex;

                return (
                  <Tabs.Tab
                    disabled={isDisabled}
                    key={key}
                    badge={updateCounter}
                    selected={isSelected}
                    onSelect={() => setActiveActivityTab(tabIndex)}
                  >
                    {tabConfig.icon && <span className="mr-2 h-5 w-5">{tabConfig.icon}</span>}
                    {tabConfig.label}
                  </Tabs.Tab>
                );
              };

              if (tabConfig.subscriptionPlanFeatureName) {
                return (
                  <FeatureLimits key={tabConfig.label} featureName={tabConfig.subscriptionPlanFeatureName}>
                    {({ isLoading }) => renderTabElement(tabConfig.label, isLoading)}
                  </FeatureLimits>
                );
              }

              return renderTabElement(tabConfig.label);
            })}
          </Tabs>
        </div>
      </div>
      <div className="h-full overflow-hidden" aria-label={selectedTab.label} role="tabpanel">
        {selectedTab.content}
      </div>
    </div>
  );
};

export const IssueCoordinationSpace = (props: IssueCoordinationSpaceProps) => (
  <SelectedIssueProvider initialValue={props.issue}>
    <IssueCoordinationSpacePrivate {...props} key={props.issue.id} />
  </SelectedIssueProvider>
);
