import React from 'react';
import type { IssueListItemSchema } from '@shape-construction/api/src/types';
import { MapPinIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { truncatedLocationPath } from 'app/components/Utils/locations';
import { useProjectLocations } from 'app/queries/projects/locations';

export type IssueCardLocationProps = {
  className?: string;
  issue: IssueListItemSchema;
};

const IssueCardLocation: React.FC<IssueCardLocationProps> = ({ className, issue }) => {
  const { data: locations } = useProjectLocations(issue.projectId);
  const locationPath = React.useMemo(
    () => truncatedLocationPath(locations, issue.locationId),
    [locations, issue.locationId]
  );

  return (
    <div className={cn('flex items-center gap-1.5 overflow-hidden text-ellipsis whitespace-nowrap', className)}>
      <MapPinIcon className="h-4 w-4 text-gray-400" />
      <span className="text-xs font-medium leading-4 text-gray-500">{locationPath}</span>
    </div>
  );
};

export default React.memo(IssueCardLocation);
