import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Table from '@shape-construction/arch-ui/src/Table';
import type { TableHeaderProps } from '@shape-construction/arch-ui/src/Table/components/TableHeader';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { TaskIDVisibilityMessage } from '../../plan/components/fields/ActivityName/TaskIDVisibilityMessage';

export const ActivityNameHeader: React.FC<TableHeaderProps> = ({ className, ...rest }) => {
  const messages = useMessageGetter('weeklyPlanner.workPlans.planEditor.header');

  return (
    <Table.Header className={cn(className, 'min-w-40 max-w-[200px]')} {...rest}>
      <div className="flex items-center">
        {messages('activityName')} <TaskIDVisibilityMessage />
      </div>
    </Table.Header>
  );
};
