import React, { type ComponentPropsWithoutRef } from 'react';
import { MagnifyingGlassIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useDebounceCallback } from '@shape-construction/hooks';

export type SearchInputProps = ComponentPropsWithoutRef<'input'>;

export const SearchInput = React.forwardRef<HTMLInputElement, SearchInputProps>(
  ({ placeholder = 'Search', onChange, className, ...props }, ref) => {
    const debouncedOnChange = useDebounceCallback(onChange!, 250);

    return (
      <div className={cn('relative', className)}>
        <input
          className={cn(
            'h-full w-full py-2 pl-9 pr-3 rounded-2xl bg-neutral-subtle border border-neutral-subtle placeholder:text-neutral-subtlest ring-0 sm:text-sm',
            'focus:placeholder-gray-400 focus:ring-indigo-500'
          )}
          placeholder={placeholder}
          type="search"
          name="searchInput"
          onChange={debouncedOnChange}
          ref={ref}
          {...props}
        />

        <div className="text-neutral-subtle pointer-events-none absolute inset-y-0 left-2 flex items-center">
          <MagnifyingGlassIcon className="h-5 w-5" aria-hidden="true" />
        </div>
      </div>
    );
  }
);
SearchInput.displayName = 'SearchInput';
