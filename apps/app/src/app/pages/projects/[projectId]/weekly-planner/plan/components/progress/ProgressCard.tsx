import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { WeeklyWorkPlanSchema } from '@shape-construction/api/src/types';
import Button from '@shape-construction/arch-ui/src/Button';
import Card from '@shape-construction/arch-ui/src/Card';
import { PencilIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import * as ProgressBar from '@shape-construction/arch-ui/src/ProgressBar';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useMediaQuery } from '@shape-construction/hooks';
import { parseDateWithFormat } from '@shape-construction/utils/DateTime';
import { useCurrentProject } from 'app/contexts/currentProject';
import { useShiftActivityProgressLog } from 'app/queries/progressLogs/progressLogs';
import type { PlanActivityWithProgressLog } from '../../hooks/usePlanActivitiesWithProgressLogs';
import { useProgressDrawerState } from '../../hooks/useProgressDrawerState';
import { BadgeComponent } from '../BadgeComponent/BadgeComponent';
import { ProgressCardCarousel } from '../ProgressCardCarousel/ProgressCardCarousel';

type ProgressCardProps = {
  plan: WeeklyWorkPlanSchema;
  planActivityWithProgressLog: PlanActivityWithProgressLog;
  onEditProgress?: () => void;
};

export const ProgressCard: React.FC<ProgressCardProps> = ({ plan, planActivityWithProgressLog, onEditProgress }) => {
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const messages = useMessageGetter('weeklyPlanner.workPlans.progressTracker.card');
  const project = useCurrentProject();
  const { progressLog } = planActivityWithProgressLog;
  const [showPrevious, setShowPrevious] = React.useState(false);
  const { data: progressLogDetails } = useShiftActivityProgressLog(
    project.id,
    progressLog?.shiftActivityId!,
    progressLog?.id!,
    {
      query: {
        enabled: showPrevious && Boolean(progressLog?.id),
      },
    }
  );
  const { openDrawer } = useProgressDrawerState();

  const updateProgress = () => {
    openDrawer(planActivityWithProgressLog);

    if (onEditProgress) {
      onEditProgress();
    }
  };

  const currentPercentage = progressLog?.percentageCompleted ?? 0;
  const previousPercentage = progressLogDetails?.previousLog?.percentageCompleted ?? 0;
  const percentageChange = currentPercentage - previousPercentage;

  return (
    <>
      <Card size="small" className="border-x-0 border-y md:border rounded-none md:rounded-md">
        <Card.Header
          border
          title={planActivityWithProgressLog.shiftActivity.description}
          subtitle={messages('expectingProgress', {
            percent: planActivityWithProgressLog.expectedPercentageCompleted ?? 0,
            day: parseDateWithFormat(plan.endDate, 'DD-MMM-YYYY (ddd)'),
          })}
        />
        <Card.Body className="w-full px-4 pt-4 pb-1 space-y-4">
          <section
            className={cn('space-y-1', {
              'max-w-[398px]': isLargeScreen,
            })}
          >
            <ProgressBar.Root progress={currentPercentage} color="success">
              <ProgressBar.Header showProgress>
                <ProgressBar.Title>
                  {messages('progressOnThisDay')}
                  {showPrevious && <BadgeComponent percentageChange={percentageChange} />}
                </ProgressBar.Title>
              </ProgressBar.Header>
            </ProgressBar.Root>
            {showPrevious && progressLogDetails?.previousLog.date && (
              <ProgressBar.Root progress={previousPercentage} size="small">
                <ProgressBar.Header showProgress>
                  <ProgressBar.Subtitle>
                    {messages('previousProgress', {
                      day: parseDateWithFormat(progressLogDetails?.previousLog.date, 'DD-MMM-YYYY'),
                    })}
                  </ProgressBar.Subtitle>
                </ProgressBar.Header>
              </ProgressBar.Root>
            )}
            {!showPrevious && (
              <Button size="xxs" variant="outlined" color="secondary" onClick={() => setShowPrevious(true)}>
                {messages('showPreviousCTA')}
              </Button>
            )}
          </section>
          <div>{progressLog?.comment}</div>
          <ProgressCardCarousel progressLog={progressLog!} />
        </Card.Body>
        <Card.Footer>
          {plan?.availableActions?.edit === true && (
            <div className="flex items-center gap-2 w-full">
              <Button
                fullWidth
                color="secondary"
                size="md"
                variant="outlined"
                leadingIcon={PencilIcon}
                onClick={updateProgress}
              >
                {messages('editCTA')}
              </Button>
            </div>
          )}
        </Card.Footer>
      </Card>
    </>
  );
};
