import React, { useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { LocationSchema } from '@shape-construction/api/src/types';
import Button from '@shape-construction/arch-ui/src/Button';
import { ChevronRightIcon, MapPinIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import Modal from '@shape-construction/arch-ui/src/Modal';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { LocationSelector } from 'app/components/LocationSelector/LocationSelector';
import { truncatedLocationPath } from 'app/components/Utils/locations';

export interface ActivityLocationProps {
  locations?: LocationSchema[];
  setLocation: (locationId: string | null) => void;
  locationId?: LocationSchema['id'] | null;
}

export const ActivityLocation: React.FC<ActivityLocationProps> = ({ locations, setLocation, locationId }) => {
  const messages = useMessageGetter('shiftReport.form');
  const [selectedLocation, setSelectedLocation] = useState<ActivityLocationProps['locationId']>(locationId);
  const [openModal, setOpenModal] = useState(false);

  const closeModal = () => {
    setOpenModal(false);
  };

  const submitLocation = () => {
    setLocation(selectedLocation!);
    closeModal();
  };

  return (
    <>
      <button
        type="button"
        onClick={() => setOpenModal(true)}
        className={cn(
          'overflow-hidden rounded-md border shadow-xs border-gray-300 bg-white h-[38px] w-full outline-solid outline-0 focus:border-indigo-500 focus:outline-indigo-500 focus:-outline-offset-2 focus:outline-2',
          { 'cursor-not-allowed opacity-50': !locations }
        )}
        disabled={!locations}
      >
        <div className="flex items-center justify-center h-full px-3 py-2">
          <div className="flex-1  text-left">
            {!locationId && <span className="text-gray-400">{messages('selectLocation')}</span>}

            {locationId && (
              <div className="flex gap-x-2">
                <MapPinIcon className="h-5 w-5 text-gray-400" />
                <span className="truncate">{truncatedLocationPath(locations, locationId)}</span>
              </div>
            )}
          </div>

          <div className="ml-5 shrink-0">
            <ChevronRightIcon className="h-4 w-4 text-gray-600" aria-hidden="true" />
          </div>
        </div>
      </button>

      {openModal && (
        <Modal.Root open onClose={closeModal}>
          <Modal.Header onClose={closeModal}>
            <Modal.Title>{messages('locationModal.title')}</Modal.Title>
          </Modal.Header>
          <Modal.Content className="-mx-6">
            <LocationSelector
              locations={locations!}
              defaultLocationId={selectedLocation || null}
              name="location_id"
              onSelect={(_, locationValue) => setSelectedLocation(locationValue)}
            />
          </Modal.Content>
          <Modal.Footer>
            <Button type="reset" color="secondary" size="md" variant="outlined" onClick={closeModal}>
              {messages('locationModal.cancelCTA')}
            </Button>
            <Button
              type="submit"
              color="primary"
              variant="contained"
              size="md"
              disabled={!selectedLocation}
              onClick={submitLocation}
            >
              {messages('locationModal.saveCTA')}
            </Button>
          </Modal.Footer>
        </Modal.Root>
      )}
    </>
  );
};
