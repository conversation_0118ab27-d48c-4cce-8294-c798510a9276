// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { ShiftActivityProgressLogBasicDetailsSchema } from '../types/shiftActivityProgressLogBasicDetailsSchema';
import { faker } from '@faker-js/faker';

export function createShiftActivityProgressLogBasicDetails(
  data?: Partial<ShiftActivityProgressLogBasicDetailsSchema>
): ShiftActivityProgressLogBasicDetailsSchema {
  faker.seed([100]);
  return {
    ...{
      id: faker.string.uuid(),
      comment: faker.string.alpha(),
      description: faker.string.alpha(),
      createdAt: faker.date.anytime().toISOString(),
      createdById: faker.number.int(),
      date: faker.date.anytime().toISOString().substring(0, 10),
      documentCount: faker.number.int(),
      lastUpdatedAt: faker.date.anytime().toISOString(),
      lastUpdatedById: faker.number.int(),
      percentageCompleted: faker.number.float(),
      quantity: faker.number.float(),
      units: faker.string.alpha(),
      shiftActivityId: faker.string.uuid(),
      trackedIn: {
        id: faker.string.uuid(),
        type: faker.helpers.arrayElement<
          NonNullable<NonNullable<ShiftActivityProgressLogBasicDetailsSchema>['trackedIn']>['type']
        >(['shift_report', 'weekly_work_plan']),
      },
    },
    ...(data || {}),
  };
}
