import React from 'react';
import type { IssueApproverSchema } from '@shape-construction/api/src/types';
import { UserAvatar } from '@shape-construction/arch-ui/src/Avatar';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import { ClockIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { CheckCircleIcon, TrashIcon, XCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { PersonItem } from 'app/components/PersonItem/PersonItem';
import { ISSUE_APPROVERS_STATE } from 'app/constants/IssueStates';

function renderStatus(status: any) {
  switch (status) {
    case ISSUE_APPROVERS_STATE.PENDING:
      return <ClockIcon className="h-full w-5 text-gray-400" data-testid="status-pending-icon" />;
    case ISSUE_APPROVERS_STATE.APPROVED:
      return <CheckCircleIcon className="h-full w-5 text-green-500" data-testid="status-approved-icon" />;
    case ISSUE_APPROVERS_STATE.REJECTED:
      return <XCircleIcon className="h-full w-5 text-xs text-red-500" data-testid="status-rejected-icon" />;
    default:
      return null;
  }
}

interface ApproverItemProps {
  approver: IssueApproverSchema;
  showBorder?: boolean;
  showPadding?: boolean;
  handleDelete?: (name: IssueApproverSchema['user']['name']) => void;
}

export const ApproverItem = ({
  showBorder = false,
  showPadding = false,
  approver,
  handleDelete,
}: ApproverItemProps) => {
  const { id, user, team, status } = approver;

  return (
    <div
      data-cy="approver-item"
      className={cn('flex w-full gap-x-3', {
        'px-4 py-5': showPadding,
        '-m-px border-b border-t': showBorder,
      })}
    >
      <div className="flex flex-1 justify-between">
        <PersonItem
          key={id}
          size="sm"
          avatar={<UserAvatar user={user} size="md" />}
          primaryLabel={user.name}
          secondaryLabel={team.displayName || ''}
        />
        <div className="flex h-full gap-x-4">
          {renderStatus(status)}
          {handleDelete && (
            <IconButton
              color="secondary"
              variant="text"
              icon={TrashIcon}
              onClick={() => handleDelete(user.name)}
              data-testid="delete-approver-button"
              size="md"
            />
          )}
        </div>
      </div>
    </div>
  );
};
