/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiLoginRefreshMutationResponseSchema } from '../types/postApiLoginRefreshSchema';
import { createPostApiLoginRefreshMutationResponse } from '../factories/createPostApiLoginRefresh';
import { http } from 'msw';

export function postApiLoginRefreshMockHandler(
  data?:
    | PostApiLoginRefreshMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('/api/login/refresh', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiLoginRefreshMutationResponse(data)), {
      status: 204,
    });
  });
}
