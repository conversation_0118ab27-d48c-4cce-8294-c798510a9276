import React from 'react';
import type {
  PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityIdMutationRequestSchema,
  ProjectSchema,
  WeeklyWorkPlanActivitySchema,
  WeeklyWorkPlanSchema,
} from '@shape-construction/api/src/types';
import Table from '@shape-construction/arch-ui/src/Table';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useUpdateWeeklyWorkPlanActivity } from 'app/queries/weeklyWorkPlans/weeklyWorkPlans';
import { lookbackColumns, useLookbackColumnOptions } from './componentTypes';

export type LookbackProps = {
  planActivity: WeeklyWorkPlanActivitySchema;
  plan: WeeklyWorkPlanSchema;
  projectId: ProjectSchema['id'];
  striped?: boolean;
};

export const LookbackActivityRow: React.FC<LookbackProps> = ({ planActivity, plan, projectId, striped = true }) => {
  const { mutate: updateActivity } = useUpdateWeeklyWorkPlanActivity();
  const onUpdateActivity =
    (id: WeeklyWorkPlanActivitySchema['id']) =>
    (values: PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityIdMutationRequestSchema) => {
      updateActivity({
        projectId,
        weeklyWorkPlanId: plan.id,
        activityId: id,
        data: values,
      });
    };

  const { lookbackColumnOptions } = useLookbackColumnOptions(plan);

  if (!planActivity) return null;

  return (
    <Table.Row
      striped={striped}
      key={planActivity.id}
      className="border-b border-b-gray-300 last:border-b-0 hover:bg-gray-100 group/activity"
    >
      {lookbackColumns.map((field) => {
        const Cell = lookbackColumnOptions[field].cell || Table.Cell;
        const Component = lookbackColumnOptions[field].component;

        return (
          <Cell
            key={`${planActivity.id}/${field}`}
            className={cn(
              'px-2 py-1 sm:first:pl-2 sm:last:pr-2',
              'leading-5 border-r border-r-gray-300 last:border-r-0 align-top',
              'text-sm font-normal text-gray-800'
            )}
          >
            {Component && (
              <Component
                plan={plan}
                planActivity={planActivity}
                onUpdateActivity={onUpdateActivity(planActivity.id)}
                isEditable={plan?.availableActions?.edit === true}
              />
            )}
          </Cell>
        );
      })}
    </Table.Row>
  );
};
