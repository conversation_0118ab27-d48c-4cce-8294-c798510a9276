import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema, ShiftActivitySchema } from '@shape-construction/api/src/types';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import { InboxOutIcon } from '@shape-construction/arch-ui/src/Icons/custom';
import { InboxArrowDownIcon, PencilSquareIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useModal } from '@shape-construction/hooks';
import { ActivityReadinessAction } from 'app/pages/projects/[projectId]/activities/components/ActivityReadinessAction';
import { Link, useLocation, useNavigate, useParams } from 'react-router';
import { ArchiveActivityConfirmationModal } from '../ArchiveActivityConfirmationModal/ArchiveActivityConfirmationModal';
import { RestoreActivityConfirmationModal } from '../RestoreActivityConfirmationModal/RestoreActivityConfirmationModal';
import { ActivityCardDetails } from './ActivityCardDetails';

type Params = {
  projectId: ProjectSchema['id'];
};

export interface ActivityCardProps {
  shiftActivity: ShiftActivitySchema;
  isSelected: boolean;
}

export const ActivityCard: React.FC<ActivityCardProps> = ({ shiftActivity, isSelected }) => {
  const messages = useMessageGetter('activities');
  const {
    open: isArchiveConfirmationModalOpen,
    openModal: openArchiveConfirmationModal,
    closeModal: closeArchiveConfirmationModal,
  } = useModal(false);
  const {
    open: isRestoreConfirmationModalOpen,
    openModal: openRestoreConfirmationModal,
    closeModal: closeRestoreConfirmationModal,
  } = useModal(false);

  const location = useLocation();
  const navigate = useNavigate();

  const handleArchiveActivity = async (event: React.SyntheticEvent) => {
    event.preventDefault();
    openArchiveConfirmationModal();
  };

  const handleRestoreActivity = async (event: React.SyntheticEvent) => {
    event.preventDefault();
    openRestoreConfirmationModal();
  };

  const { projectId } = useParams<Params>() as Params;
  const activityUrl = {
    pathname: `/projects/${projectId}/activities/${shiftActivity.id}`,
  };

  const editActivityUrl = `/projects/${projectId}/activities/${shiftActivity.id}/edit`;

  const canArchiveActivity = (!shiftActivity.archived && shiftActivity.availableActions.archive) || false;
  const canEditActivity = shiftActivity.availableActions.edit;
  const canRestoreActivity = (shiftActivity.archived && shiftActivity.availableActions.restore) || false;

  return (
    <>
      <Link
        aria-current={isSelected}
        aria-label="activity card"
        data-testid={`activity-card-${shiftActivity.id}`}
        className={cn('flex flex-wrap md:flex-nowrap gap-2 p-4 bg-white rounded-md items-center', {
          'ring-2 ring-indigo-500': isSelected,
        })}
        to={activityUrl}
        state={{ background: location }}
      >
        <ActivityCardDetails shiftActivity={shiftActivity} />
        <div className="shrink-0 flex flex-row gap-2">
          <ActivityReadinessAction shiftActivity={shiftActivity} />
          {canEditActivity && (
            <IconButton
              aria-label="edit activity"
              title="edit activity"
              color="secondary"
              size="sm"
              variant="text"
              icon={PencilSquareIcon}
              onClick={(e) => {
                e.preventDefault();
                navigate(editActivityUrl, {
                  state: { edit: true, background: location },
                });
              }}
            />
          )}
          {canArchiveActivity && (
            <IconButton
              aria-label={messages('activityCard.archiveCTA')}
              title={messages('activityCard.archiveCTA')}
              color="secondary"
              size="sm"
              variant="text"
              icon={InboxArrowDownIcon}
              onClick={handleArchiveActivity}
            />
          )}
          {canRestoreActivity && (
            <IconButton
              aria-label={messages('activityCard.restoreCTA')}
              title={messages('activityCard.restoreCTA')}
              color="secondary"
              size="sm"
              variant="text"
              icon={InboxOutIcon}
              onClick={handleRestoreActivity}
            />
          )}
        </div>
      </Link>
      <ArchiveActivityConfirmationModal
        isOpen={isArchiveConfirmationModalOpen}
        onClose={closeArchiveConfirmationModal}
        shiftActivityId={shiftActivity.id}
      />
      <RestoreActivityConfirmationModal
        isOpen={isRestoreConfirmationModalOpen}
        onClose={closeRestoreConfirmationModal}
        shiftActivityId={shiftActivity.id}
      />
    </>
  );
};
