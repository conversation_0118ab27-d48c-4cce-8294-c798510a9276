import React, { useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ShiftActivityStatusSchema } from '@shape-construction/api/src/types';
import Badge from '@shape-construction/arch-ui/src/Badge';
import type { THEME } from '@shape-construction/arch-ui/src/Badge/Badge.types';
import * as Select from '@shape-construction/arch-ui/src/Select';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useActivityStatusOptions } from 'app/components/Activities/hooks/useActivityStatusOptions';
import { ACTIVITY_STATUS, ACTIVITY_STATUS_COLORS } from 'app/components/ShiftManager/Activities/constants';
import type { FieldProps } from '../types';

type Item = {
  name: string;
  value: ShiftActivityStatusSchema;
};

const ActivityStatusBadge: React.FC<{ status: ShiftActivityStatusSchema }> = ({ status }) => {
  const optionsMessages = useMessageGetter('weeklyPlanner.workPlans.planEditor.activityStatus.options');

  return <Badge theme={ACTIVITY_STATUS_COLORS[status] as THEME} label={optionsMessages(status)} />;
};

type ActivityStatusProps = Omit<FieldProps, 'onUpdateActivity'>;

export const ActivityStatus: React.FC<ActivityStatusProps> = ({ planActivity, onUpdateShiftActivity, isEditable }) => {
  const shiftActivityStatus = planActivity.shiftActivity.status;
  const activityStatus = shiftActivityStatus === null ? ACTIVITY_STATUS.NOT_STARTED : shiftActivityStatus;
  const [selectedStatus, setSelectedStatus] = useState<ShiftActivityStatusSchema>(
    activityStatus as ShiftActivityStatusSchema
  );

  const options = useActivityStatusOptions();

  const handleOnChange = (value: ShiftActivityStatusSchema) => {
    setSelectedStatus(value);
    onUpdateShiftActivity({ status: value });
  };

  if (!isEditable) {
    return (
      <div className="whitespace-nowrap py-2">
        <ActivityStatusBadge status={selectedStatus} />
      </div>
    );
  }

  return (
    <Select.Root value={selectedStatus} onChange={handleOnChange}>
      <Select.Trigger
        as="div"
        variant="plain"
        size="sm"
        className={cn(
          'group hover:text-gray-800 hover:ring-2 hover:ring-gray-400 focus:ring-2 focus:ring-indigo-500 active:ring-2 active:ring-indigo-500',
          'bg-transparent focus:bg-white hover:bg-white'
        )}
        showChevronOnHover
      >
        <ActivityStatusBadge status={selectedStatus} />
      </Select.Trigger>

      <Select.Panel>
        <Select.Options>
          {options.map((option) => (
            <Select.Option key={option.name} value={option.value}>
              <Select.OptionText>{option.name}</Select.OptionText>
            </Select.Option>
          ))}
        </Select.Options>
      </Select.Panel>
    </Select.Root>
  );
};
