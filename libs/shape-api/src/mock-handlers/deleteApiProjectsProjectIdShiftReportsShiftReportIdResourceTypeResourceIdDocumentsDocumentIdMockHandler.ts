/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsDocumentIdMutationResponseSchema } from '../types/deleteApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsDocumentIdSchema';
import { createDeleteApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsDocumentIdMutationResponse } from '../factories/createDeleteApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsDocumentId';
import { http } from 'msw';

export function deleteApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsDocumentIdMockHandler(
  data?:
    | DeleteApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsDocumentIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete(
    '/api/projects/:project_id/shift_reports/:shift_report_id/:resource_type/:resource_id/documents/:document_id',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data ||
            createDeleteApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsDocumentIdMutationResponse(
              data
            )
        ),
        {
          status: 204,
        }
      );
    }
  );
}
