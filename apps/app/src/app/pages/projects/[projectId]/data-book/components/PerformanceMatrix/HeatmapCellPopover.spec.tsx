import React from 'react';
import MessageFormat from '@messageformat/core';
import { dataHealthScoreFactory } from '@shape-construction/api/factories/dataHealthScores';
import listFormatter from 'libs/i18n/message-format/formatters/listFormatter';
import { render, screen, userEvent, within } from 'tests/test-utils';
import { HeatmapCellPopover } from './HeatmapCellPopover';

describe('<HeatmapCellPopover />', () => {
  describe('when user hovers over a trigger', () => {
    it('shows the popover with score and record count', async () => {
      const dataPoint = dataHealthScoreFactory({ recordCount: 54, score: 85 });
      const messageFormat = new MessageFormat('en', { customFormatters: { list: listFormatter } });
      const messages = {
        dataBook: {
          page: {
            heatmapDashboard: {
              heatmap: {
                cell: {
                  popover: {
                    publishedRecords: messageFormat.compile('Count: {recordCount}'),
                    averageScore: messageFormat.compile('Score: {score}%'),
                  },
                },
              },
            },
          },
        },
      };
      render(
        <HeatmapCellPopover dataPoint={dataPoint}>
          <button type="button">Trigger</button>
        </HeatmapCellPopover>,
        { messages }
      );

      userEvent.hover(screen.getByRole('button', { name: 'Trigger' }));

      const popover = await screen.findByRole('tooltip', {
        name: 'dataBook.page.heatmapDashboard.heatmap.cell.popover.label',
      });
      expect(popover).toBeInTheDocument();
      expect(await within(popover).findByText('Count: 54')).toBeInTheDocument();
      expect(await within(popover).findByText('Score: 85%')).toBeInTheDocument();
    });
  });

  describe('when record count is 0', () => {
    it('shows no record count message', async () => {
      const dataPoint = dataHealthScoreFactory({ recordCount: 0 });
      render(
        <HeatmapCellPopover dataPoint={dataPoint}>
          <button type="button">Trigger</button>
        </HeatmapCellPopover>
      );

      userEvent.hover(screen.getByRole('button', { name: 'Trigger' }));

      const popover = await screen.findByRole('tooltip', {
        name: 'dataBook.page.heatmapDashboard.heatmap.cell.popover.label',
      });
      expect(
        await within(popover).findByText('dataBook.page.heatmapDashboard.heatmap.cell.popover.noPublishedRecord')
      ).toBeInTheDocument();
    });
  });

  describe('with a level 1 score', () => {
    it('shows the level 1 badge in the popover', async () => {
      const dataPoint = dataHealthScoreFactory({ score: 0 });
      render(
        <HeatmapCellPopover dataPoint={dataPoint}>
          <button type="button">Trigger</button>
        </HeatmapCellPopover>
      );

      userEvent.hover(screen.getByRole('button', { name: 'Trigger' }));

      const popover = await screen.findByRole('tooltip', {
        name: 'dataBook.page.heatmapDashboard.heatmap.cell.popover.label',
      });
      expect(
        await within(popover).findByRole('status', {
          name: /dataBook.page.heatmapDashboard.healthLevels.1.label/,
        })
      ).toBeInTheDocument();
    });
  });

  describe('with a level 2 score', () => {
    it('shows the level 2 badge in the popover', async () => {
      const dataPoint = dataHealthScoreFactory({ score: 25 });
      render(
        <HeatmapCellPopover dataPoint={dataPoint}>
          <button type="button">Trigger</button>
        </HeatmapCellPopover>
      );

      userEvent.hover(screen.getByRole('button', { name: 'Trigger' }));

      const popover = await screen.findByRole('tooltip', {
        name: 'dataBook.page.heatmapDashboard.heatmap.cell.popover.label',
      });
      expect(
        await within(popover).findByRole('status', {
          name: /dataBook.page.heatmapDashboard.healthLevels.2.label/,
        })
      ).toBeInTheDocument();
    });
  });

  describe('with a level 3 score', () => {
    it('shows the level 3 badge in the popover', async () => {
      const dataPoint = dataHealthScoreFactory({ score: 55 });
      render(
        <HeatmapCellPopover dataPoint={dataPoint}>
          <button type="button">Trigger</button>
        </HeatmapCellPopover>
      );

      userEvent.hover(screen.getByRole('button', { name: 'Trigger' }));

      const popover = await screen.findByRole('tooltip', {
        name: 'dataBook.page.heatmapDashboard.heatmap.cell.popover.label',
      });
      expect(
        await within(popover).findByRole('status', {
          name: /dataBook.page.heatmapDashboard.healthLevels.3.label/,
        })
      ).toBeInTheDocument();
    });
  });

  describe('with a level 4 score', () => {
    it('shows the level 4 badge in the popover', async () => {
      const dataPoint = dataHealthScoreFactory({ score: 75 });
      render(
        <HeatmapCellPopover dataPoint={dataPoint}>
          <button type="button">Trigger</button>
        </HeatmapCellPopover>
      );

      userEvent.hover(screen.getByRole('button', { name: 'Trigger' }));

      const popover = await screen.findByRole('tooltip', {
        name: 'dataBook.page.heatmapDashboard.heatmap.cell.popover.label',
      });
      expect(
        await within(popover).findByRole('status', {
          name: /dataBook.page.heatmapDashboard.healthLevels.4.label/,
        })
      ).toBeInTheDocument();
    });
  });

  describe('with a level 5 score', () => {
    it('shows the level 5 badge in the popover', async () => {
      const dataPoint = dataHealthScoreFactory({ score: 90 });
      render(
        <HeatmapCellPopover dataPoint={dataPoint}>
          <button type="button">Trigger</button>
        </HeatmapCellPopover>
      );

      userEvent.hover(screen.getByRole('button', { name: 'Trigger' }));

      const popover = await screen.findByRole('tooltip', {
        name: 'dataBook.page.heatmapDashboard.heatmap.cell.popover.label',
      });
      expect(
        await within(popover).findByRole('status', {
          name: /dataBook.page.heatmapDashboard.healthLevels.5.label/,
        })
      ).toBeInTheDocument();
    });
  });
});
