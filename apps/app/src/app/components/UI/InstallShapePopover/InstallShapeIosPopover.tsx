import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import { InstallIcon } from '@shape-construction/arch-ui/src/Icons/custom';
import Popover from '@shape-construction/arch-ui/src/Popover';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useMediaQuery, useModal } from '@shape-construction/hooks';
import { useInstallApp } from 'app/hooks/useInstallApp';

export const InstallShapeIosPopover = () => {
  const messages = useMessageGetter('installShape.iOS');
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const { open, closeModal } = useModal(true);
  const { commitInstallAppPromptClosed, isInStandaloneMode, isInstallAppPromptClosed, isSafariOnNonDesktop } =
    useInstallApp();

  const onClose = () => {
    closeModal();
    commitInstallAppPromptClosed();
  };

  const hideInstallShape = !isSafariOnNonDesktop || isInStandaloneMode || isInstallAppPromptClosed;

  if (hideInstallShape) {
    return null;
  }

  return (
    <Popover open={open}>
      <Popover.Trigger
        className={cn('fixed h-0 select-none', isLargeScreen ? 'top-2 right-2 w-64' : 'bottom-0 w-full')}
      />
      <Popover.Content
        align={isLargeScreen ? 'end' : 'center'}
        side={isLargeScreen ? 'bottom' : 'top'}
        onClose={onClose}
        className="max-w-sm"
        sideOffset={2}
      >
        <Popover.Content.Body>
          <div>
            <h1 className="font-medium text-base pb-1">{messages('title')}</h1>
            <span>{messages('descriptionPrefix')}</span>
            <InstallIcon className="inline h-3.5 mb-1 mx-1" />
            <span>{messages('descriptionPostfix')}</span>
          </div>
        </Popover.Content.Body>
      </Popover.Content>
    </Popover>
  );
};
