/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PatchApiUsersPasswordMutationResponseSchema } from '../types/patchApiUsersPasswordSchema';
import { createPatchApiUsersPasswordMutationResponse } from '../factories/createPatchApiUsersPassword';
import { http } from 'msw';

export function patchApiUsersPasswordMockHandler(
  data?:
    | PatchApiUsersPasswordMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.patch>[1]>[0]) => Response)
) {
  return http.patch('/api/users/password', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPatchApiUsersPasswordMutationResponse(data)), {
      status: 204,
    });
  });
}
