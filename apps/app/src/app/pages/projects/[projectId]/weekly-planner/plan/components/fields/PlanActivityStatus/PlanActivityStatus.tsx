import React, { useMemo, useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { WeeklyWorkPlanActivityStatusesSchema } from '@shape-construction/api/src/types';
import Badge from '@shape-construction/arch-ui/src/Badge';
import type { THEME } from '@shape-construction/arch-ui/src/Badge/Badge.types';
import * as Select from '@shape-construction/arch-ui/src/Select';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import {
  PLAN_ACTIVITY_STATUS,
  PLAN_ACTIVITY_STATUS_COLORS,
} from 'app/pages/projects/[projectId]/weekly-planner/plan/constants/constants';
import type { FieldProps } from '../types';

type Item = {
  name: string;
  value: Exclude<WeeklyWorkPlanActivityStatusesSchema, 'additional'>;
};

const PlanActivityStatusBadge: React.FC<{ status: WeeklyWorkPlanActivityStatusesSchema }> = ({ status }) => {
  const optionsMessages = useMessageGetter('weeklyPlanner.workPlans.planEditor.planActivityStatus.options');

  return <Badge theme={PLAN_ACTIVITY_STATUS_COLORS[status] as THEME} label={optionsMessages(status)} />;
};

export type PlanActivityStatusProps = Pick<FieldProps, 'planActivity' | 'onUpdateActivity' | 'isEditable'>;

export const PlanActivityStatus: React.FC<PlanActivityStatusProps> = ({
  planActivity,
  onUpdateActivity,
  isEditable,
}) => {
  const [selectedStatus, setSelectedStatus] = useState(planActivity.status);

  const optionsMessages = useMessageGetter('weeklyPlanner.workPlans.planEditor.planActivityStatus.options');

  const options: Item[] = useMemo(() => {
    return Object.values(PLAN_ACTIVITY_STATUS)
      .map((status) => {
        if (status === 'additional') return;

        return {
          name: optionsMessages(status),
          value: status,
        };
      })
      .filter((el) => el !== undefined) as Item[];
  }, [optionsMessages]);

  const handleOnChange = (value: WeeklyWorkPlanActivityStatusesSchema) => {
    setSelectedStatus(value);
    onUpdateActivity({ status: value });
  };

  if (!isEditable) {
    return (
      <div className="whitespace-nowrap py-2">
        <PlanActivityStatusBadge status={selectedStatus} />
      </div>
    );
  }

  return (
    <Select.Root value={selectedStatus} onChange={handleOnChange}>
      <Select.Trigger
        as="div"
        variant="plain"
        size="sm"
        className={cn(
          'group hover:text-gray-800 hover:ring-2 hover:ring-gray-400 focus:ring-2 focus:ring-indigo-500 active:ring-2 active:ring-indigo-500',
          'bg-transparent focus:bg-white hover:bg-white'
        )}
        showChevronOnHover
      >
        <PlanActivityStatusBadge status={selectedStatus} />
      </Select.Trigger>

      <Select.Panel>
        <Select.Options>
          {options.map((option) => (
            <Select.Option key={option.name} value={option.value}>
              <Select.OptionText>{option.name}</Select.OptionText>
            </Select.Option>
          ))}
        </Select.Options>
      </Select.Panel>
    </Select.Root>
  );
};
