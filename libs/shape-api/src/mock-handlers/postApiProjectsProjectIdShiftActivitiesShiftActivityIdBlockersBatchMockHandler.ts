/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatchMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatchSchema';
import { createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatchMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatch';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatchMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatchMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post(
    '/api/projects/:project_id/shift_activities/:shift_activity_id/blockers/batch',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data || createPostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatchMutationResponse(data)
        ),
        {
          status: 204,
        }
      );
    }
  );
}
