import React from 'react';
import Table from '@shape-construction/arch-ui/src/Table';
import type { TableHeaderProps } from '@shape-construction/arch-ui/src/Table/components/TableHeader';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';

export const ResponsiblePersonHeader: React.FC<TableHeaderProps> = ({ className, ...rest }) => (
  <Table.Header className={cn(className, 'min-w-[150px] max-w-[200px]')} {...rest} />
);
