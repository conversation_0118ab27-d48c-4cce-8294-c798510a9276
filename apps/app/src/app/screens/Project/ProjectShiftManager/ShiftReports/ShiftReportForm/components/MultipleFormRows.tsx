import React, { useCallback, useEffect, useRef } from 'react';
import Button from '@shape-construction/arch-ui/src/Button';
import EmptyState from '@shape-construction/arch-ui/src/EmptyState';
import { PlusCircleIcon, PlusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { Spinner } from '@shape-construction/arch-ui/src/Spinner/Spinner';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import type { FieldArrayWithId } from 'react-hook-form';
import type { ShiftReportRowsPathname } from '../ShiftReportForm';

export type MultipleFormRowsProps = {
  path: ShiftReportRowsPathname;
  addRowLabel: string;
  isAddingRow?: boolean;
  fields: (FieldArrayWithId<any, any, 'id'> & FieldArrayWithId<any, any, 'uuid'>)[];
  formRowComponent: React.ElementType;
  textButton?: boolean;
  onAddRow: () => void;
  icon?: React.ReactElement;
  emptyDescription?: string;
  emptySubDescription?: string;
  showEmptyState?: boolean;
  hideAddRow?: boolean;
};
export const MultipleFormRows = ({
  path,
  addRowLabel,
  isAddingRow = false,
  textButton = false,
  fields,
  formRowComponent: FormRowComponent,
  onAddRow,
  icon,
  emptyDescription,
  emptySubDescription,
  showEmptyState,
  hideAddRow,
}: MultipleFormRowsProps) => {
  const rowsLength = fields.length;
  const sectionRef = useRef<HTMLDivElement>(null);
  const prevFieldsRef = useRef<typeof fields>([]);
  const isLocalAddRef = useRef(false);

  useEffect(() => {
    // Check if this is a local add (user clicked add button) vs remote add (other user added row)
    if (rowsLength > prevFieldsRef.current.length) {
      // This is a new row being added
      if (isLocalAddRef.current) {
        // This was added locally, scroll to it
        // a small delay fixed an erratic behaviour on Safari
        setTimeout(() => {
          if (sectionRef.current) {
            sectionRef.current.scrollIntoView({ behavior: 'smooth' });
          }
        }, 100);
        // Reset the flag after scrolling
        isLocalAddRef.current = false;
      }
    }
    prevFieldsRef.current = fields;
  }, [rowsLength, fields]);

  const handleAddItem = useCallback(
    (event: React.MouseEvent<HTMLButtonElement>) => {
      event.preventDefault();
      // Mark that this is a local add
      isLocalAddRef.current = true;
      onAddRow();
    },
    [onAddRow]
  );

  const renderEmptyState = showEmptyState && rowsLength === 0;

  return (
    <div className={`flex flex-col ${hideAddRow ? '' : 'pt-4 pb-2'}`}>
      {fields.map((field, fieldIndex) => {
        const isLastItem = fieldIndex === rowsLength - 1;
        const formRowStyle = cn('pt-2', { 'pb-2': isLastItem });
        return (
          <div key={field.id} ref={isLastItem ? sectionRef : null} className={formRowStyle}>
            <FormRowComponent field={field} index={fieldIndex} path={`${path}.${fieldIndex}`} />
          </div>
        );
      })}
      {!hideAddRow &&
        (renderEmptyState ? (
          <EmptyState
            body={
              <div className="flex flex-col gap-y-2">
                <div>{emptyDescription}</div>
                <div>{emptySubDescription}</div>
              </div>
            }
            icon={icon}
          >
            <EmptyState.PrimaryAction onClick={handleAddItem} leadingIcon={PlusIcon} variant="outlined">
              {addRowLabel}
            </EmptyState.PrimaryAction>
          </EmptyState>
        ) : (
          <div>
            <Button
              variant={textButton ? 'text' : 'outlined'}
              fullWidth={!textButton}
              size={textButton ? 'sm' : 'xl'}
              color="primary"
              disabled={isAddingRow}
              leadingIcon={isAddingRow ? Spinner : PlusCircleIcon}
              onClick={handleAddItem}
            >
              {addRowLabel}
            </Button>
          </div>
        ))}
    </div>
  );
};
