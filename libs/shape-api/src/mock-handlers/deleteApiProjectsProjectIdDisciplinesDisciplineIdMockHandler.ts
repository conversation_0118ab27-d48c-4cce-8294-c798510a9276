/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdDisciplinesDisciplineIdMutationResponseSchema } from '../types/deleteApiProjectsProjectIdDisciplinesDisciplineIdSchema';
import { createDeleteApiProjectsProjectIdDisciplinesDisciplineIdMutationResponse } from '../factories/createDeleteApiProjectsProjectIdDisciplinesDisciplineId';
import { http } from 'msw';

export function deleteApiProjectsProjectIdDisciplinesDisciplineIdMockHandler(
  data?:
    | DeleteApiProjectsProjectIdDisciplinesDisciplineIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete('/api/projects/:project_id/disciplines/:discipline_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createDeleteApiProjectsProjectIdDisciplinesDisciplineIdMutationResponse(data)),
      {
        status: 204,
      }
    );
  });
}
