import { disciplineValidationSchema } from 'app/pages/projects/[projectId]/issues/components/IssueCoordinationSpace/components/DisciplineSelector/DisciplineSelector';
import type { FormikConfig } from 'formik';
import { Discipline, type DisciplineFormValues } from './Discipline';

type GenerateFormPropsType = (initialValues: {
  initialValues: DisciplineFormValues;
}) => Partial<FormikConfig<DisciplineFormValues>>;

export const generateFormProps: GenerateFormPropsType = ({ initialValues = { disciplineId: '' } }) => {
  const formProps: Partial<FormikConfig<DisciplineFormValues>> = {
    initialValues: {
      disciplineId: initialValues.disciplineId || '',
    },
    validationSchema: disciplineValidationSchema(),
  };

  return formProps;
};

export default Discipline;
