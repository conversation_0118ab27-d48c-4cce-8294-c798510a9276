import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ShiftReportActivitySchema, ShiftReportResourceAllocationSchema } from '@shape-construction/api/src/types';
import Table from '@shape-construction/arch-ui/src/Table';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';

const getLinksToRender = (
  links: ShiftReportResourceAllocationSchema[] | undefined,
  resources: ShiftReportActivitySchema[]
) => {
  const linksToRender: {
    description: ShiftReportActivitySchema['description'];
    quantity: ShiftReportResourceAllocationSchema['quantity'];
  }[] = [];

  const activitiesById = new Map<ShiftReportActivitySchema['id'], ShiftReportActivitySchema>(
    resources.map((resource) => [resource.id, resource])
  );

  links?.forEach((link) => {
    if (link.allocationId) {
      linksToRender.push({
        description: activitiesById.get(link.allocationId)?.description ?? null,
        quantity: link.quantity,
      });
    }
  });

  return linksToRender;
};

export interface ActivityLinkPreviewProps {
  activityLinks?: ShiftReportResourceAllocationSchema[];
  activities: ShiftReportActivitySchema[];
  hideBorder?: boolean;
}
export const ActivityLinkPreview: React.FC<ActivityLinkPreviewProps> = ({
  activityLinks,
  activities,
  hideBorder = false,
}) => {
  const messages = useMessageGetter('shiftReport.form');

  const activityLinksToRender = getLinksToRender(activityLinks, activities);

  if (activityLinksToRender.length === 0) return null;

  return (
    <Table.Row
      className={cn({
        'border-b last:border-b-0': !hideBorder,
      })}
    >
      <Table.Cell>{messages('progress')}:</Table.Cell>
      <Table.Cell colSpan={4}>
        {activityLinksToRender.map((linkToRender, index) => {
          const activity = linkToRender.quantity != null ? ` (${linkToRender.quantity})` : '';

          return index === 0 ? `${linkToRender.description}${activity}` : `, ${linkToRender.description}${activity}`;
        })}
      </Table.Cell>
    </Table.Row>
  );
};
