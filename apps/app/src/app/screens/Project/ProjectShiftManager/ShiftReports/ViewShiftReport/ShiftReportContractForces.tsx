import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ShiftReportSchema } from '@shape-construction/api/src/types';
import Table from '@shape-construction/arch-ui/src/Table';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { FormSectionHeader } from '../components/FormSection';
import { ActivityLinkPreview } from './components/ActivityLinkPreview';
import { DownTimeLinkPreview } from './components/DownTimeLinkPreview';
import { ResourcesDocumentsPreview } from './components/ResourcesDocumentsPreview';

type ShiftReportContractForcesProps = {
  shiftReport: ShiftReportSchema;
};

export const ShiftReportContractForces = ({
  shiftReport: { contractForces, activities, downTimes, projectId, id: shiftReportId },
}: ShiftReportContractForcesProps) => {
  const messages = useMessageGetter('shiftReport.form');

  return (
    <fieldset className="grid grid-cols-1 gap-6">
      <legend>
        <FormSectionHeader className="mb-4">{messages('people')}</FormSectionHeader>
      </legend>

      <div
        className="overflow-auto rounded-md shadow ring-1 ring-black/5
         [&_div]:overflow-visible [&_div]:shadow-none [&_div]:ring-0"
      >
        <Table.Container>
          <Table>
            <Table.Heading>
              <Table.Row className="[&_th]:pl-3 [&_th]:normal-case">
                <Table.Header>{messages('name')}</Table.Header>
                <Table.Header>{messages('role')}</Table.Header>
                <Table.Header>{messages('organisation')}</Table.Header>
                <Table.Header>{messages('hours')}</Table.Header>
                <Table.Header>{messages('comment')} </Table.Header>
              </Table.Row>
            </Table.Heading>
            <Table.Body className="[&_td]:text-gray-800">
              {contractForces.length ? (
                contractForces.map((contractForce) => {
                  const {
                    id,
                    name,
                    role,
                    organisation,
                    hours,
                    comment,
                    activities: activityLinks,
                    downTimes: downTimeLinks,
                  } = contractForce;
                  return (
                    <React.Fragment key={id}>
                      <Table.Row
                        className={cn({
                          'border-b last:border-b-0': !activityLinks?.length && !downTimeLinks?.length,
                        })}
                      >
                        <Table.Cell>{name}</Table.Cell>
                        <Table.Cell>{role}</Table.Cell>
                        <Table.Cell>{organisation}</Table.Cell>
                        <Table.Cell>{hours}</Table.Cell>
                        <Table.Cell colSpan={4} className="whitespace-pre-line">
                          {comment}
                        </Table.Cell>
                      </Table.Row>
                      {!!contractForce.documentCount && (
                        <Table.Row>
                          <Table.Cell colSpan={6} width={100} className="pt-0 [&_div]:overflow-x-auto">
                            <ResourcesDocumentsPreview
                              projectId={projectId}
                              shiftReportId={shiftReportId}
                              resource={contractForce}
                              resourceType="contract_forces"
                            />
                          </Table.Cell>
                        </Table.Row>
                      )}
                      <ActivityLinkPreview
                        activityLinks={activityLinks}
                        activities={activities}
                        hideBorder={!!downTimeLinks?.length}
                      />
                      <DownTimeLinkPreview downTimeLinks={downTimeLinks} downTimes={downTimes} />
                    </React.Fragment>
                  );
                })
              ) : (
                <Table.Row>
                  <Table.Cell colSpan={5}>
                    <span className="text-gray-400">{messages('noEntries')}</span>
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table>
        </Table.Container>
      </div>
    </fieldset>
  );
};
