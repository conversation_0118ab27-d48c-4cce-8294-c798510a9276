import React from 'react';
import type { IssueSchema } from '@shape-construction/api/src/types';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';

export type IssueCardUniquRefProps = {
  className?: string;
  uniqueRef: IssueSchema['referenceNumber'];
};

const IssueCardUniquRef = ({ className, uniqueRef }: IssueCardUniquRefProps) => (
  <span className={cn('whitespace-nowrap text-xs leading-4 font-medium text-gray-500', className)}>{uniqueRef}</span>
);

export default React.memo(IssueCardUniquRef);
