import React from 'react';
import { useMessage } from '@messageformat/react';
import type { ProjectSchema } from '@shape-construction/api/src/types';
import StackedListItem from '@shape-construction/arch-ui/src/StackedListItem';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useProjectPeople } from 'app/queries/projects/people';
import { Link } from 'react-router';

export interface ArchivedProjectsListItemProps {
  project: ProjectSchema;
}

export const ArchivedProjectsListItem: React.FC<ArchivedProjectsListItemProps> = ({ project }) => {
  const { data: projectTeamMembers } = useProjectPeople(project.id);
  const teamMembers = projectTeamMembers?.map(({ user }) => user) || [];
  const members = teamMembers?.filter((item) => item?.avatarUrl).slice(0, 4) ?? [];

  const membersSurroundClasses = cn('flex -space-x-1 overflow-hidden', {
    'mr-2': teamMembers?.length > 0,
  });

  return (
    <StackedListItem>
      <Link to={`/projects/${project.id}/issues`} className="flex flex-1 flex-col gap-4 md:flex-row">
        <div role="listitem" aria-label="project item" className="flex w-full flex-wrap items-center gap-4 text-left">
          <div className="inline-flex h-10 w-10 flex-none items-center justify-center rounded-md bg-gray-400">
            {project.logoUrl ? (
              <img src={project.logoUrl} alt={project.title} className="h-10 w-10 rounded-md object-cover" />
            ) : (
              <span className="text-sm leading-none text-white">{project.shortName}</span>
            )}
          </div>
          <div className="flex flex-1 flex-col text-left">
            <p className="text-sm font-medium">{project.title}</p>
            <p className="flex items-center text-sm text-gray-500">{project.timezone}</p>
          </div>
          <div className="flex flex-1 space-x-1 sm:space-x-4">
            <div className={membersSurroundClasses}>
              {members.map((teamMember) => (
                <img
                  key={teamMember.id}
                  className="inline-block h-6 w-6 rounded-full ring-2 ring-white"
                  src={teamMember.avatarUrl || ''}
                  alt={teamMember.name || ''}
                />
              ))}
            </div>
            <p className="whitespace-nowrap text-sm text-gray-500">
              {useMessage('projects.members', { count: teamMembers?.length })}
            </p>
          </div>
        </div>
      </Link>
    </StackedListItem>
  );
};
