import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Link from '@shape-construction/arch-ui/src/Link';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { useCurrentProject } from 'app/contexts/currentProject';
import { useLocation, useNavigate } from 'react-router';
import type { FieldPropsWithPlan } from '../types';

type ActivityReadinessRequirementsProps = Pick<FieldPropsWithPlan, 'planActivity' | 'plan'>;

export const ActivityReadinessRequirements: React.FC<ActivityReadinessRequirementsProps> = ({ planActivity, plan }) => {
  const project = useCurrentProject();
  const shiftActivityId = planActivity.shiftActivity.id;
  const completedRequirements = planActivity.shiftActivity.readiness.completedRequirements;
  const totalRequirements = planActivity.shiftActivity.readiness.totalRequirements;

  const location = useLocation();
  const navigate = useNavigate();

  const goToReadiness = () => {
    navigate(`/projects/${project.id}/weekly-planner/plans/${plan.id}/activity/${shiftActivityId}/readiness`, {
      state: { background: location.pathname },
    });
  };

  const message = useMessageGetter('weeklyPlanner.workPlans.planEditor.requirements');

  return (
    <div className="flex justify-center py-2">
      <Tooltip.Root>
        <Tooltip.Trigger aria-label={message('tooltip')}>
          <Link as="button" color={'primary'} onClick={goToReadiness}>
            {totalRequirements === 0
              ? message('none')
              : message('count', {
                  completed: completedRequirements,
                  total: totalRequirements,
                })}
          </Link>
        </Tooltip.Trigger>
        <Tooltip.Content side="top">{message('tooltip')}</Tooltip.Content>
      </Tooltip.Root>
    </div>
  );
};
