import { useMessage, useMessageGetter } from '@messageformat/react';
import {
  getApiProjectsProjectIdIssuesGroupCountQueryKey,
  getApiProjectsProjectIdIssuesIssueIdQueryKey,
  getApiProjectsProjectIdIssuesQueryKey,
  usePostApiProjectsProjectIdIssuesIssueIdAssignments,
  usePostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAccept,
  usePostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdReject,
} from '@shape-construction/api/src/hooks';
import { showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { useQueryClient } from '@tanstack/react-query';

export const useIssueAssignment: typeof usePostApiProjectsProjectIdIssuesIssueIdAssignments = (options) => {
  const queryClient = useQueryClient();
  const toastMessage = useMessageGetter('issue.detail.assignments');

  return usePostApiProjectsProjectIdIssuesIssueIdAssignments({
    ...options,
    mutation: {
      ...options?.mutation,
      onSuccess: (issueAssignment, { projectId, issueId }) => {
        showSuccessToast({
          message: toastMessage('assignedTo', {
            name: issueAssignment.assignee.user.name,
          }),
        });
        queryClient.invalidateQueries({
          queryKey: getApiProjectsProjectIdIssuesQueryKey(projectId),
        });
        queryClient.invalidateQueries({
          queryKey: getApiProjectsProjectIdIssuesGroupCountQueryKey(projectId),
          exact: false,
        });
        queryClient.invalidateQueries({
          queryKey: getApiProjectsProjectIdIssuesIssueIdQueryKey(projectId, issueId),
        });
      },
    },
  });
};

export const useIssueAcceptAssignment: typeof usePostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAccept =
  (options) => {
    const queryClient = useQueryClient();
    const toastMessage = useMessage('issue.detail.assignments.accept');

    return usePostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAccept({
      ...options,
      mutation: {
        ...options?.mutation,
        onSuccess: (_, { projectId, issueId }) => {
          showSuccessToast({
            message: toastMessage,
          });
          queryClient.invalidateQueries({
            queryKey: getApiProjectsProjectIdIssuesQueryKey(projectId),
          });
          queryClient.invalidateQueries({
            queryKey: getApiProjectsProjectIdIssuesGroupCountQueryKey(projectId),
            exact: false,
          });
          queryClient.invalidateQueries({
            queryKey: getApiProjectsProjectIdIssuesIssueIdQueryKey(projectId, issueId),
          });
        },
      },
    });
  };

export const useIssueRejectAssignment: typeof usePostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdReject =
  (options) => {
    const queryClient = useQueryClient();
    const toastMessage = useMessage('issue.detail.assignments.reject');

    return usePostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdReject({
      ...options,
      mutation: {
        ...options?.mutation,
        onSuccess: (_, { projectId, issueId }) => {
          showSuccessToast({
            message: toastMessage,
          });
          queryClient.invalidateQueries({
            queryKey: getApiProjectsProjectIdIssuesQueryKey(projectId),
          });
          queryClient.invalidateQueries({
            queryKey: getApiProjectsProjectIdIssuesGroupCountQueryKey(projectId),
            exact: false,
          });
          queryClient.invalidateQueries({
            queryKey: getApiProjectsProjectIdIssuesIssueIdQueryKey(projectId, issueId),
          });
        },
      },
    });
  };
