version: 2
updates:
  # Root package.json (pnpm workspace)
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "friday"
      time: "00:00"
      timezone: "Europe/Lisbon"
    open-pull-requests-limit: 5
    groups:
      all-dependencies:
        patterns:
          - "*"
    assignees:
      - "bot-the-builder-shape"
  
  # Web app (apps/app)
  - package-ecosystem: "npm"
    directory: "/apps/app"
    schedule:
      interval: "weekly"
      day: "friday"
      time: "04:00"
      timezone: "Europe/Lisbon"
    open-pull-requests-limit: 5
    groups:
      all-dependencies:
        patterns:
          - "*"
    assignees:
      - "bot-the-builder-shape"
  
  # Mobile app (apps/channels) - React Native/Expo
  - package-ecosystem: "npm"
    directory: "/apps/channels"
    schedule:
      interval: "weekly"
      day: "friday"
      time: "08:00"
      timezone: "Europe/Lisbon"
    open-pull-requests-limit: 5
    groups:
      all-dependencies:
        patterns:
          - "*"
    assignees:
      - "bot-the-builder-shape"
  
  # UI Library (libs/arch-ui)
  - package-ecosystem: "npm"
    directory: "/libs/arch-ui"
    schedule:
      interval: "weekly"
      day: "friday"
      time: "12:00"
      timezone: "Europe/Lisbon"
    open-pull-requests-limit: 5
    groups:
      all-dependencies:
        patterns:
          - "*"
    assignees:
      - "bot-the-builder-shape"
  
  # Native UI Library (libs/arch-ui-native)
  - package-ecosystem: "npm"
    directory: "/libs/arch-ui-native"
    schedule:
      interval: "weekly"
      day: "sunday"
      time: "00:00"
      timezone: "Europe/Lisbon"
    open-pull-requests-limit: 5
    groups:
      all-dependencies:
        patterns:
          - "*"
    assignees:
      - "bot-the-builder-shape"
  
  # Feature Flags Library (libs/feature-flags)
  - package-ecosystem: "npm"
    directory: "/libs/feature-flags"
    schedule:
      interval: "weekly"
      day: "saturday"
      time: "00:00"
      timezone: "Europe/Lisbon"
    open-pull-requests-limit: 5
    groups:
      all-dependencies:
        patterns:
          - "*"
    assignees:
      - "bot-the-builder-shape"
  
  # React Image Markup Library (libs/react-image-markup)
  - package-ecosystem: "npm"
    directory: "/libs/react-image-markup"
    schedule:
      interval: "weekly"
      day: "saturday"
      time: "04:00"
      timezone: "Europe/Lisbon"
    open-pull-requests-limit: 5
    groups:
      all-dependencies:
        patterns:
          - "*"
    assignees:
      - "bot-the-builder-shape"
  
  # API Client Library (libs/shape-api)
  - package-ecosystem: "npm"
    directory: "/libs/shape-api"
    schedule:
      interval: "weekly"
      day: "saturday"
      time: "08:00"
      timezone: "Europe/Lisbon"
    open-pull-requests-limit: 5
    groups:
      all-dependencies:
        patterns:
          - "*"
    assignees:
      - "bot-the-builder-shape"
  
  # Hooks Library (libs/shape-hooks)
  - package-ecosystem: "npm"
    directory: "/libs/shape-hooks"
    schedule:
      interval: "weekly"
      day: "sunday"
      time: "04:00"
      timezone: "Europe/Lisbon"
    open-pull-requests-limit: 5
    groups:
      all-dependencies:
        patterns:
          - "*"
    assignees:
      - "bot-the-builder-shape"
  
  # Utils Library (libs/shape-utils)
  - package-ecosystem: "npm"
    directory: "/libs/shape-utils"
    schedule:
      interval: "weekly"
      day: "sunday"
      time: "08:00"
      timezone: "Europe/Lisbon"
    open-pull-requests-limit: 5
    groups:
      all-dependencies:
        patterns:
          - "*"
    assignees:
      - "bot-the-builder-shape"
  
  # Terraform infrastructure
  - package-ecosystem: "terraform"
    directories:
      - "/infrastructure/*"
    schedule:
      interval: "monthly"
      day: "friday"
      time: "20:00"
      timezone: "Europe/Lisbon"
    open-pull-requests-limit: 5
    assignees:
      - "bot-the-builder-shape"
      - "shape-construction/sre"
