import { useMemo } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { QueuedTaskResultFileDownloadSchema, QueuedTaskSchema } from '@shape-construction/api/src/types';
import Button from '@shape-construction/arch-ui/src/Button';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { exportsAtom } from './state/exportsAtom';
import useQueueToasts from './useQueueToasts';
import { type ExportOperationName, type ExportQueuedTask, isExportJob } from './utils';

const useExports = () => {
  const messages = useMessageGetter('exports');

  const operationName = useMemo(
    () =>
      ({
        issue_export: messages('operationName.issue'),
        issue_list_export: messages('operationName.issue_list'),
        potential_change_export: messages('operationName.potential_change'),
        shift_activity_list_export: messages('operationName.shift_activity_list'),
        shift_report_export: messages('operationName.shift_report'),
        shift_report_list_export: messages('operationName.shift_report_list'),
        weekly_work_plan_export: messages('operationName.weekly_work_plan'),
        weekly_work_plan_lookback_export: messages('operationName.weekly_work_plan_lookback'),
      }) satisfies Record<ExportOperationName, string>,
    [messages]
  );

  const createAction = (job: ExportQueuedTask) => {
    if (!job.result) return null;

    return (
      <Tooltip.Root>
        <Tooltip.Trigger asChild>
          <a href={job.result.fileUrl} download target="_blank" rel="noreferrer">
            <Button variant="outlined" color="white" size="sm" onClick={() => removeFromCompletedJobs(job.id)}>
              <span className="flex flex-row">
                <span className="shrink-0">
                  {messages('download')}
                  {` "`}
                </span>
                <span className="flex-1 break-all line-clamp-1">{job.result.filename}</span>"
              </span>
            </Button>
          </a>
        </Tooltip.Trigger>
        <Tooltip.Content side="bottom">{job.result.filename}</Tooltip.Content>
      </Tooltip.Root>
    );
  };

  const toastMessages = {
    success: messages('toasts.success'),
    loading: (job: ExportQueuedTask) => {
      return messages('toasts.loading', { exportType: operationName[job.operation] });
    },
    failed: (job: ExportQueuedTask) => {
      return messages('toasts.failed', { exportType: operationName[job.operation] });
    },
  };

  const {
    addToPendingJobs,
    addToCompletedJobs,
    removeFromCompletedJobs,
    jobQueue,
    initToastsFromStorage,
    showFailedJobToast,
  } = useQueueToasts<ExportQueuedTask>('exports', exportsAtom, toastMessages, createAction);

  const exportsJobQueue = useMemo(() => jobQueue.filter(isExportJob), [jobQueue]);

  const triggerDownload = (file: QueuedTaskResultFileDownloadSchema['result']) => {
    if (!file) return;

    const link = document.createElement('a');
    link.href = file.fileUrl;
    link.download = file.filename ?? 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getTimeLeft = (expiryDateStr: string) => {
    const currentDate = new Date();
    const expiryTime = new Date(expiryDateStr).getTime();
    const timeLeft = expiryTime - currentDate.getTime();

    return timeLeft;
  };

  const isValidExpiry = (expiryDateStr: string) => {
    return getTimeLeft(expiryDateStr) > 0;
  };

  const addToPendingExports = (job: QueuedTaskSchema) => {
    if (!isExportJob(job)) return;

    addToPendingJobs(job);
  };

  const addToCompletedExports = (job: QueuedTaskSchema) => {
    if (!isExportJob(job)) return;

    addToCompletedJobs(job, getTimeLeft(job.result?.expiresAt as string));
    triggerDownload(job.result);
  };

  const dismissFailedExport = (job: QueuedTaskSchema) => {
    if (!isExportJob(job)) return;

    if (job.status === 'failed' || job.status === 'expired') {
      removeFromCompletedJobs(job.id);
      showFailedJobToast(job);
    }
  };

  const initExportToastsFromStorage = () => {
    const filteredExports = exportsJobQueue.filter((job) => {
      if (job.status === 'pending') return true;

      if (job.status === 'completed' && job.result?.expiresAt && isValidExpiry(job.result?.expiresAt)) {
        return true;
      }

      return false;
    });

    initToastsFromStorage(filteredExports);
  };

  const pendingExportIds = useMemo(() => {
    return exportsJobQueue.filter((job) => job.status === 'pending').map((job) => job.id);
  }, [exportsJobQueue]);

  return {
    addToPendingExports,
    addToCompletedExports,
    removeFromCompletedExports: removeFromCompletedJobs,
    exportsQueue: exportsJobQueue,
    initToastsFromStorage: initExportToastsFromStorage,
    dismissFailedExport,
    pendingExportIds,
  };
};

export default useExports;
