const esmModules = [
  'react-native',
  'escape-string-regexp',
  'expo-router',
  '@gorhom/bottom-sheet',
  '@react-native',
  '@react-native(-community)',
  'expo(nent)?',
  '@expo(nent)?/.*',
  '@expo-google-fonts/.*',
  '@react-navigation/.*',
  '@unimodules/.*',
  'unimodules',
  'sentry-expo',
  '@sentry/react-native',
  'native-base',
  'react-native-svg',
  '@notifee/react-native',
  '@rn-primitives/*',
  'stream-chat',
  'stream-chat-expo',
  'p-locate',
  'p-limit',
  'yocto-queue',
  'mime',
];

const path = require('path');

module.exports = {
  collectCoverageFrom: ['src/**/*.{js,jsx,ts,tsx}', '!src/**/*.d.ts', 'libs/**/*.{js,jsx,ts,tsx}', '!libs/**/*.d.ts'],
  coverageReporters: ['lcov'],
  modulePaths: ['<rootDir>'],
  preset: 'jest-expo',
  roots: ['<rootDir>'],
  setupFiles: [
    '<rootDir>/src/tests/setupTestsEnv.ts',
    '<rootDir>/../../node_modules/@react-native-google-signin/google-signin/jest/build/jest/setup.js',
  ],
  setupFilesAfterEnv: ['<rootDir>/src/tests/setupTests.ts'],
  silent: !!process.env.CI,
  transformIgnorePatterns: [`node_modules/(?!(?:.pnpm/)?(${esmModules.join('|')}))`],
  // https://github.com/expo/expo/issues/26513#issuecomment-1989035903
  transform: {
    '\\.[jt]sx?$': ['babel-jest', { caller: { preserveEnvVars: true } }],
  },
  moduleNameMapper: {
    '^stream-chat$': require.resolve('stream-chat'),
  },
};
