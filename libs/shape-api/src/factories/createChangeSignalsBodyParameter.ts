// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { ChangeSignalsBodyParameterSchema } from '../types/changeSignalsBodyParameterSchema';
import { faker } from '@faker-js/faker';

export function createChangeSignalsBodyParameter(
  data?: Partial<ChangeSignalsBodyParameterSchema>
): ChangeSignalsBodyParameterSchema {
  faker.seed([100]);
  return {
    ...{
      change_signals: faker.helpers.multiple(() => ({
        change_signal_type: faker.helpers.arrayElement<
          NonNullable<
            NonNullable<NonNullable<ChangeSignalsBodyParameterSchema>['change_signals']>[number]
          >['change_signal_type']
        >(['issue', 'downtime']),
        change_signal_id: faker.string.uuid(),
      })),
    },
    ...(data || {}),
  };
}
