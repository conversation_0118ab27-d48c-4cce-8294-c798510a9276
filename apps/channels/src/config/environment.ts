import Constants from 'expo-constants';

export const environment = {
  APP_ENV: process.env.EXPO_PUBLIC_APP_ENV,
  API_URL: process.env.EXPO_PUBLIC_API_URL,
  SHAPE_APP_URL: process.env.EXPO_PUBLIC_SHAPE_APP_URL,
  CHANNELS_TEAM_INVITE_URL: process.env.EXPO_PUBLIC_CHANNELS_TEAM_INVITE_URL,
  GOOGLE_WEB_CLIENT_ID: process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID,
  GETSTREAM_CHAT_ID: process.env.EXPO_PUBLIC_GETSTREAM_CHAT_ID,
  PUSH_NOTIFICATIONS_PROVIDER: process.env.EXPO_PUBLIC_PUSH_NOTIFICATIONS_PROVIDER,
  PUSH_NOTIFICATIONS_PROVIDER_NAME: process.env.EXPO_PUBLIC_PUSH_NOTIFICATIONS_PROVIDER_NAME,
  SENTRY_DSN: process.env.EXPO_PUBLIC_SENTRY_DSN,
  VERSION: Constants.expoConfig?.version,
  AZURE_APP_CLIENT_ID: process.env.EXPO_PUBLIC_AZURE_APP_CLIENT_ID,

  // Feature flags
  FEATURE_FLAG_CAPTURE_NOTES: process.env.EXPO_PUBLIC_FEATURE_FLAG_CAPTURE_NOTES === 'true',
};
