/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdDocumentsDocumentIdMutationResponseSchema } from '../types/deleteApiProjectsProjectIdDocumentsDocumentIdSchema';
import { createDeleteApiProjectsProjectIdDocumentsDocumentIdMutationResponse } from '../factories/createDeleteApiProjectsProjectIdDocumentsDocumentId';
import { http } from 'msw';

export function deleteApiProjectsProjectIdDocumentsDocumentIdMockHandler(
  data?:
    | DeleteApiProjectsProjectIdDocumentsDocumentIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete('/api/projects/:project_id/documents/:document_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createDeleteApiProjectsProjectIdDocumentsDocumentIdMutationResponse(data)),
      {
        status: 204,
      }
    );
  });
}
