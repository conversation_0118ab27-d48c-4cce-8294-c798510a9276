import { useMessageGetter } from '@messageformat/react';
import type { IssueEventSchema, ProjectSchema } from '@shape-construction/api/src/types';
import { UserAvatar } from '@shape-construction/arch-ui/src/Avatar';
import { formatDateAndTime } from '@shape-construction/utils/DateTime';
import { parser } from '@shape-construction/utils/dom';
import { EventIcon } from 'app/components/UI/EventIcon/EventIcon';
import { ISSUE_EVENT_TYPES } from 'app/constants/IssueEventTypes';
import type { EventItem } from 'app/lib/utils/batch-events';
import changeCase from 'change-case';

interface TimelineEventCardHeaderProps {
  type: IssueEventSchema['type'];
  actor: IssueEventSchema['owner'];
  date: IssueEventSchema['date'];
  parameters: IssueEventSchema['parameters'];
  multiple: EventItem['multiple'];
  timezone: ProjectSchema['timezone'];
}

export const TimelineEventCardHeader: React.FC<TimelineEventCardHeaderProps> = ({
  type,
  actor,
  date,
  parameters,
  multiple,
  timezone,
}) => {
  const messages = useMessageGetter('timeline.events.card.title');

  const generateTitle = () => {
    switch (type) {
      case ISSUE_EVENT_TYPES.COMMENT_ON:
        // @ts-ignore FIXME
        return parameters.body
          ? messages('commentOn', { number: 1 })
          : // @ts-ignore FIXME
            messages('commentOn', { number: parameters.comments.length });
      case ISSUE_EVENT_TYPES.PRIVATE_COMMENT_ON:
        // @ts-ignore FIXME
        return parameters.body
          ? messages('privateCommentOn', { number: 1 })
          : // @ts-ignore FIXME
            messages('privateCommentOn', { number: parameters.comments.length });
      case ISSUE_EVENT_TYPES.UPLOAD_IMAGE:
        return messages('uploadImage', {
          // @ts-ignore FIXME
          number: parameters.imageUrls.length < 2 ? 0 : parameters.imageUrls.length,
        });
      case ISSUE_EVENT_TYPES.CREATE:
        return messages('create');
      case ISSUE_EVENT_TYPES.APPROVE:
        return messages('approve');
      case ISSUE_EVENT_TYPES.REOPEN:
        return messages('reopen');
      case ISSUE_EVENT_TYPES.CHANGE_STATUS:
        return messages('changeStatus');
      case ISSUE_EVENT_TYPES.REJECT_RESOLUTION:
        return messages('rejectResolution');
      default:
        return changeCase.titleCase(type);
    }
  };

  return (
    <div className="flex flex-col sm:flex-row justify-between sm:items-center px-2 py-4 gap-1 border-b border-gray-200">
      <div className="flex items-center">
        <div className="flex items-center gap-x-1 align-middle">
          <EventIcon eventType={type} />

          {parser(
            messages('eventTitle', {
              title: generateTitle(),
              multiple: multiple,
            }),
            {
              replace: (node) => {
                const elemName = (node as any).name;
                if (elemName === 'user' && !multiple) {
                  return (
                    <div className="flex items-center gap-x-1">
                      <UserAvatar user={actor} size="xs" />
                      <span className="text-base font-semibold">{actor.name}</span>
                    </div>
                  );
                }
              },
            }
          )}
        </div>
      </div>
      <div className="text-xs text-gray-400">{formatDateAndTime(date, timezone)}</div>
    </div>
  );
};
