/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdTeamsTeamIdResendMembersInvitesMutationResponseSchema } from '../types/postApiProjectsProjectIdTeamsTeamIdResendMembersInvitesSchema';
import { createPostApiProjectsProjectIdTeamsTeamIdResendMembersInvitesMutationResponse } from '../factories/createPostApiProjectsProjectIdTeamsTeamIdResendMembersInvites';
import { http } from 'msw';

export function postApiProjectsProjectIdTeamsTeamIdResendMembersInvitesMockHandler(
  data?:
    | PostApiProjectsProjectIdTeamsTeamIdResendMembersInvitesMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('/api/projects/:project_id/teams/:team_id/resend_members_invites', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdTeamsTeamIdResendMembersInvitesMutationResponse(data)),
      {
        status: 202,
      }
    );
  });
}
