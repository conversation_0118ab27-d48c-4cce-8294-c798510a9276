// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueDetailsBasicSchema } from '../types/issueDetailsBasicSchema';
import { createIssueCategory } from './createIssueCategory';
import { createIssueImpact } from './createIssueImpact';
import { createIssueState } from './createIssueState';
import { createIssueVisibilityStatus } from './createIssueVisibilityStatus';
import { faker } from '@faker-js/faker';

export function createIssueDetailsBasic(data?: Partial<IssueDetailsBasicSchema>): IssueDetailsBasicSchema {
  faker.seed([100]);
  return {
    ...{
      archived: faker.datatype.boolean(),
      assignedTeamMemberId: faker.number.int(),
      category: Object.assign({}, createIssueCategory()),
      closedAt: faker.date.anytime().toISOString(),
      createdAt: faker.date.anytime().toISOString(),
      critical: faker.datatype.boolean(),
      currentState: createIssueState(),
      delayFinish: faker.date.anytime().toISOString(),
      delayStart: faker.date.anytime().toISOString(),
      description: faker.string.alpha(),
      disciplineId: faker.string.uuid(),
      draftAssigneeId: faker.number.int(),
      dueDate: faker.date.anytime().toISOString(),
      id: faker.string.uuid(),
      immediateAction: faker.string.alpha(),
      impact: Object.assign({}, createIssueImpact()),
      locationId: faker.string.uuid(),
      nextActionerId: faker.number.int(),
      observedAt: faker.date.anytime().toISOString(),
      observerId: faker.number.int(),
      originatorId: faker.number.int(),
      overdue: faker.datatype.boolean(),
      peopleInvolvedSafety: faker.string.alpha(),
      plannedClosureDate: faker.date.anytime().toISOString(),
      potentialImpactSeverity: faker.string.alpha(),
      preventativeAction: faker.string.alpha(),
      projectId: faker.string.uuid(),
      qualityScore: faker.number.int(),
      referenceNumber: faker.string.alpha(),
      safetyAlert: faker.datatype.boolean(),
      safetyLikelihoodScore: faker.number.int(),
      subCategory: faker.string.alpha(),
      title: faker.string.alpha(),
      updatedAt: faker.date.anytime().toISOString(),
      visibilityStatus: Object.assign({}, createIssueVisibilityStatus()),
      workAffected: faker.string.alpha(),
    },
    ...(data || {}),
  };
}
