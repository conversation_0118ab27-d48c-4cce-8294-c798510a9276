import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Badge from '@shape-construction/arch-ui/src/Badge';
import { SHAPE } from '@shape-construction/arch-ui/src/Badge/Badge.types';
import { MinusCircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import {
  ArrowDownIcon,
  ArrowUpIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
} from '@shape-construction/arch-ui/src/Icons/solid';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { parseDateWithFormat } from '@shape-construction/utils/DateTime';
import { cardStateClasses, getBadgeStateClasses, iconStateClasses, textStateClasses } from './dayLogClasses';
import type { DayLogState } from './types';

const getState = (hasLogs: boolean, hasDelta: boolean, isScheduled: boolean): DayLogState => {
  if (hasLogs && hasDelta) return 'complete';
  if (hasLogs || hasDelta || isScheduled) return 'incomplete';
  return 'not-scheduled';
};

const getTooltipMessageKey = (state: DayLogState, hasLogs: boolean, hasDelta: boolean): string => {
  if (state === 'incomplete' && hasLogs) return 'incomplete-delta';
  if (state === 'incomplete' && hasDelta) return 'incomplete-logs';

  return state;
};

const iconState = {
  complete: CheckCircleIcon,
  incomplete: ExclamationTriangleIcon,
  'incomplete-delta': ExclamationTriangleIcon,
  'incomplete-logs': ExclamationTriangleIcon,
  'not-scheduled': MinusCircleIcon,
};

type DayLogProps = React.ComponentPropsWithRef<'button'> & {
  weekDay: string;
  date: string;
  numberOfLogs: number;
  delta: number;
  isScheduled: boolean;
};

export const DayLog = React.forwardRef<HTMLButtonElement, DayLogProps>(
  ({ weekDay, date, numberOfLogs, delta, isScheduled, disabled, ...props }, ref) => {
    const messages = useMessageGetter('weeklyPlanner.workPlans.lookback.dayLog');

    const hasLogs = numberOfLogs >= 1;
    const hasDelta = delta !== 0;
    const state: DayLogState = getState(hasLogs, hasDelta, isScheduled);

    const Icon = iconState[state];
    const arrowIcon = delta && delta < 0 ? ArrowDownIcon : ArrowUpIcon;
    const showBAdges = state !== 'not-scheduled' && !disabled;

    return (
      <Tooltip.Root>
        <Tooltip.Trigger asChild>
          <button
            {...props}
            type="button"
            ref={ref}
            disabled={disabled}
            className={cn(
              'flex flex-col p-2 space-y-2.5 min-h-20 min-w-28',
              cardStateClasses[state].bg,
              cardStateClasses[state].border,
              {
                'opacity-50': disabled,
                [cardStateClasses[state].bgHover]: !disabled,
              }
            )}
          >
            <div className="flex space-x-2">
              <div className={cn('flex items-center justify-center p-[7px] rounded-sm', iconStateClasses[state].bg)}>
                <Icon width={20} height={20} className={cn(iconStateClasses[state].icon)} />
              </div>
              <div className="flex flex-col space-y-0.5 justify-between items-start">
                <div className={cn('text-sm leading-4 font-semibold', textStateClasses[state])}>{weekDay}</div>
                <div className={cn('text-sm leading-4 font-medium', textStateClasses[state])}>
                  {parseDateWithFormat(date, 'DD-MMM')}
                </div>
              </div>
            </div>
            {showBAdges && (
              <div className="flex justify-between space-x-1">
                <Badge
                  label={messages('numberOfLogs', { numberOfLogs })}
                  theme={getBadgeStateClasses(hasLogs).theme}
                  shape={SHAPE.ROUNDED}
                  className={cn('flex-1 rounded-full', getBadgeStateClasses(hasLogs).bg)}
                />
                <Badge
                  icon={delta ? arrowIcon : undefined}
                  label={messages('delta', { delta: Math.abs(delta) })}
                  theme={getBadgeStateClasses(hasDelta).theme}
                  shape={SHAPE.ROUNDED}
                  className={cn('flex-1 rounded-full gap-x-0.5', getBadgeStateClasses(hasDelta).bg)}
                />
              </div>
            )}
          </button>
        </Tooltip.Trigger>
        <Tooltip.Content side="bottom">
          {messages(`tooltip.${getTooltipMessageKey(state, hasLogs, hasDelta)}`)}
        </Tooltip.Content>
      </Tooltip.Root>
    );
  }
);

DayLog.displayName = 'DayLog';
