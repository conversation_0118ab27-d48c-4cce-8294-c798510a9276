import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import Popover from '@shape-construction/arch-ui/src/Popover';
import { useProductTourPopover } from '../hooks/useProductTourPopover';

export type ProductTourPopoverProps = React.ComponentProps<typeof Popover.Content>;

export const ProductTourPopover: React.FC<ProductTourPopoverProps> = ({ ...popoverProps }) => {
  const popoverMessages = useMessageGetter('productTour.popover');
  const { closeProductTourPopover } = useProductTourPopover();

  return (
    <Popover.Root open>
      <Popover.Trigger className="absolute w-8 h-8" />

      <Popover.Content color="discovery" onClose={closeProductTourPopover} sideOffset={6} {...popoverProps}>
        <Popover.Content.Heading>{popoverMessages('title')}</Popover.Content.Heading>
        <Popover.Content.Body>{popoverMessages('description')}</Popover.Content.Body>
        <Popover.Content.Footer>
          <Button color="white" variant="contained" size="sm" onClick={closeProductTourPopover}>
            {popoverMessages('gotItCTA')}
          </Button>
        </Popover.Content.Footer>
      </Popover.Content>
    </Popover.Root>
  );
};
