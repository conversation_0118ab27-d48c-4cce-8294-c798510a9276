import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ShiftActivitySchema } from '@shape-construction/api/src/types';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import { XMarkIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useMediaQuery } from '@shape-construction/hooks';
import { formatDateAndTime } from '@shape-construction/utils/DateTime';

export interface ActivityHeaderProps {
  shiftActivity: ShiftActivitySchema;
  onClose: () => void;
  timezone: string;
}

export const ActivityHeader: React.FC<ActivityHeaderProps> = ({ shiftActivity, onClose, timezone }) => {
  const messages = useMessageGetter('activities.activityDetails.header');
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));

  return (
    <header className="flex flex-col gap-0.5 p-4 md:p-6">
      <div className="flex justify-end">
        <IconButton
          size="sm"
          color="secondary"
          variant="text"
          icon={XMarkIcon}
          aria-label={messages('closeCTA')}
          onClick={onClose}
        />
      </div>
      <div className="flex flex-col gap-2">
        <div
          className={cn('flex items-center', {
            'col-span-2 row-start-2 md:col-span-1': !isLargeScreen,
          })}
        >
          <h1 className="text-lg font-medium leading-7 text-gray-900 md:text-xl md:font-medium md:leading-7">
            {shiftActivity.description}
          </h1>
        </div>

        <div className="flex flex-wrap items-center gap-x-4 gap-y-2 text-gray-400 text-sm font-medium leading-5">
          <span>{messages('shapeId', { id: shiftActivity.referenceNumber })}</span>
          <span className="flex gap-x-1">
            {messages('createdOn')}
            <span className="text-gray-500">{formatDateAndTime(shiftActivity.createdAt, timezone)}</span>
          </span>
        </div>
      </div>
    </header>
  );
};
