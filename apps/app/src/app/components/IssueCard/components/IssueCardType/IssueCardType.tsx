import React, { useCallback } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { IssueSchema } from '@shape-construction/api/src/types';
import {
  ArrowPathIcon,
  ClipboardDocumentCheckIcon,
  QuestionMarkCircleIcon,
  ShieldExclamationIcon,
} from '@shape-construction/arch-ui/src/Icons/solid';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';

export type IssueSubcategory =
  | 'incomplete work'
  | 'damage'
  | 'minor deviation'
  | 'defect'
  | 'access'
  | 'material'
  | 'equipment'
  | 'design'
  | 'quality document'
  | 'safe work document'
  | 'permit or consent'
  | 'hazard'
  | 'positive observation';

type IssueTypeLabel = IssueSchema['category'] | IssueSubcategory;
type IssueTypeLabels = Record<NonNullable<IssueTypeLabel>, string>;

interface IconTypeProps {
  type: IssueTypeLabel;
  className: string;
}

interface IssueCardTypeProps {
  className?: string;
  type: IssueSchema['category'];
  subType: IssueSubcategory;
}
const IconType: React.FC<IconTypeProps> = ({ type, ...iconProps }) => {
  switch (type) {
    case 'close out':
      return <ClipboardDocumentCheckIcon {...iconProps} />;
    case 'something needed':
      return <QuestionMarkCircleIcon {...iconProps} />;
    case 'safety':
      return <ShieldExclamationIcon {...iconProps} />;
    case 'progress':
      return <ArrowPathIcon {...iconProps} />;
    default:
      return <ClipboardDocumentCheckIcon {...iconProps} />;
  }
};

const IssueCardType = ({ className, type, subType }: IssueCardTypeProps) => {
  const messages = useMessageGetter('issue.detail');
  const getIssueTypeText = useCallback(() => {
    const issueTypeLabels: IssueTypeLabels = {
      'close out': messages('types.closeOut.shortLabel'),
      'something needed': messages('types.somethingNeeded.shortLabel'),
      safety: messages('types.safety.shortLabel'),
      progress: messages('types.progress.shortLabel'),
      'incomplete work': messages('types.incompleteWork.shortLabel'),
      damage: messages('types.damage.shortLabel'),
      'minor deviation': messages('types.minorDeviation.shortLabel'),
      defect: messages('types.defect.shortLabel'),
      access: messages('types.access.shortLabel'),
      material: messages('types.material.shortLabel'),
      equipment: messages('types.equipment.shortLabel'),
      design: messages('types.design.shortLabel'),
      'quality document': messages('types.qualityDocument.shortLabel'),
      'safe work document': messages('types.safeWorkingDocument.shortLabel'),
      'permit or consent': messages('types.permitOrConsent.shortLabel'),
      hazard: messages('types.hazard.shortLabel'),
      'positive observation': messages('types.positiveObservation.shortLabel'),
    };
    if (subType) return issueTypeLabels[subType];
    if (type) return issueTypeLabels[type];
    return '';
  }, [messages, subType, type]);

  return (
    <div className={cn('flex items-center gap-1.5 overflow-hidden whitespace-nowrap', className)}>
      <IconType className="h-4 w-4 text-gray-400" type={type} />
      <span className="text-xs font-medium leading-4 text-gray-500">{getIssueTypeText()}</span>
    </div>
  );
};

export default React.memo(IssueCardType);
