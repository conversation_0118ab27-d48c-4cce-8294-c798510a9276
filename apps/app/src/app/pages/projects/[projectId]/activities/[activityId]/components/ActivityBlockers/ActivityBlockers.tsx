import { useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import { getApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersQueryOptions } from '@shape-construction/api/src/hooks';
import type { IssueSchema, IssueSummarySchema, ShiftActivitySchema } from '@shape-construction/api/src/types';
import Button from '@shape-construction/arch-ui/src/Button';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import Divider from '@shape-construction/arch-ui/src/Divider';
import { TrashIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { LinkIcon, PlusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { showErrorToast, showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { useModal } from '@shape-construction/hooks';
import { useQuery } from '@tanstack/react-query';
import IssueCardStatus from 'app/components/IssueCard/components/IssueCardStatus/IssueCardStatus';
import { Link } from 'app/components/UI/Link/Link';
import { useIssueStatesLabels } from 'app/constants/IssueStates';
import { useCurrentProject } from 'app/contexts/currentProject';
import { ActivityBlockersDrawer } from 'app/pages/projects/[projectId]/activities/components/ActivityBlockers/ActivityBlockersDrawer';
import { useCreateBatchActivityBlockers, useDeleteActivityBlocker } from 'app/queries/activities/activities';
import { BlockersSearchField } from './BlockersSearchField';
import { DeleteActivityBlockerConfirmationModal } from './DeleteActivityBlockerConfirmationModal';
import { EmptyActivityBlockers } from './EmptyActivityBlockers';

type BlockerListItemProps = {
  issue: IssueSummarySchema;
  projectId: string;
  onRemove: () => void;
  canEditActivityBlockers: boolean;
};

const BlockerListItem: React.FC<BlockerListItemProps> = ({ issue, projectId, onRemove, canEditActivityBlockers }) => {
  const messages = useMessageGetter('shiftManager.activities.readiness.blockers');
  const issueStates = useIssueStatesLabels();
  const issueStatusLabel = issueStates[issue.currentState] ?? '';

  return (
    <li className="grid gap-1 grid-cols-12 py-0.5 pr-1 items-center w-full rounded-sm hover:bg-neutral-subtlest-hovered focus-within:outline-solid outline-indigo-400">
      <div className="col-span-8">
        <Link
          target="_blank"
          rel="noreferrer"
          className="flex items-center gap-1 focus-within:outline-solid outline-indigo-400 rounded-sm"
          to={`/projects/${projectId}/issues/${issue.id}`}
        >
          <LinkIcon className="h-4 w-4 min-w-4" />
          <span className="underline truncate">{issue.title}</span>
        </Link>
      </div>
      <div className="col-span-3 truncate">
        <IssueCardStatus text={issueStatusLabel} status={issue.currentState} truncateBadge />
      </div>
      <div className="col-span-1 h-full flex items-center justify-end">
        <Tooltip.Root>
          <Tooltip.Trigger asChild>
            <IconButton
              variant="text"
              size="xxs"
              color="danger"
              shape="square"
              icon={TrashIcon}
              aria-label={messages('actions.deleteBlocker')}
              type="button"
              onClick={onRemove}
              disabled={!canEditActivityBlockers}
              aria-disabled={!canEditActivityBlockers}
            />
          </Tooltip.Trigger>
          <Tooltip.Content side="bottom" hidden={canEditActivityBlockers}>
            {messages('tooltips.noPermission.delete')}
          </Tooltip.Content>
        </Tooltip.Root>
      </div>
    </li>
  );
};

export type ActivityBlockersProps = {
  shiftActivity: ShiftActivitySchema;
};

export const ActivityBlockers: React.FC<ActivityBlockersProps> = ({ shiftActivity }) => {
  const messages = useMessageGetter('shiftManager.activities.readiness.blockers');
  const canEditActivityBlockers = shiftActivity.availableActions.edit;
  const {
    open: deleteBlockerConfirmationOpen,
    closeModal: closeDeleteBlockerConfirmation,
    openModal: openDeleteBlockerConfirmation,
  } = useModal(false);
  const {
    open: isOpenActivityBlockersDrawer,
    openModal: openActivityBlockersDrawer,
    closeModal: closeActivityBlockersDrawer,
  } = useModal(false);
  const [selectedBlockerId, setSelectedBlockerId] = useState<string | null>(null);

  const project = useCurrentProject();
  const { data: activityBlockers } = useQuery(
    getApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersQueryOptions(project.id, shiftActivity.id)
  );
  const { mutate: createBatchActivityBlockers } = useCreateBatchActivityBlockers();
  const { mutate: deleteActivityBlocker } = useDeleteActivityBlocker();

  const blockers = activityBlockers?.entries ?? [];
  const activeBlockers = blockers.filter((b) => b.issue.currentState !== 'resolved');
  const resolvedBlockers = blockers.filter((b) => b.issue.currentState === 'resolved');

  const hasBlockers = blockers.length > 0;
  const hasResolvedAndActiveBlockers = resolvedBlockers.length > 0 && activeBlockers.length > 0;

  const onAddBlocker = (issueId: IssueSchema['id']) => {
    createBatchActivityBlockers(
      { projectId: project.id, shiftActivityId: shiftActivity.id, data: { issue_ids: issueId } },
      {
        onSuccess: () => {
          showSuccessToast({
            message: messages('toasts.addSuccess'),
          });
        },
        onError: () => {
          showErrorToast({ message: messages('toasts.addError') });
        },
      }
    );
  };

  const onDeleteBlocker = (id: string) => {
    deleteActivityBlocker(
      { projectId: project.id, shiftActivityId: shiftActivity.id, shiftActivityBlockerId: id },
      {
        onSuccess: () => {
          showSuccessToast({
            message: messages('toasts.deleteSuccess'),
          });
        },
        onError: () => {
          showErrorToast({ message: messages('toasts.deleteError') });
        },
      }
    );
  };

  return (
    <>
      <div className="flex flex-col max-h-96 mt-4 md:mt-0 md:ml-4">
        <div className="flex justify-between">
          <div className="flex flex-col">
            <span className="text-sm leading-5 font-normal text-neutral-subtle">{messages('title')}</span>
          </div>
          {hasBlockers && (
            <div>
              <Tooltip.Root>
                <Tooltip.Trigger asChild>
                  <Button
                    leadingIcon={PlusIcon}
                    color="primary"
                    variant="outlined"
                    size="xxs"
                    onClick={openActivityBlockersDrawer}
                    disabled={!canEditActivityBlockers}
                    aria-disabled={!canEditActivityBlockers}
                  >
                    {messages('actions.addBlockers')}
                  </Button>
                </Tooltip.Trigger>
                <Tooltip.Content side="bottom" hidden={canEditActivityBlockers}>
                  {messages('tooltips.noPermission.create')}
                </Tooltip.Content>
              </Tooltip.Root>
            </div>
          )}
        </div>

        {!hasBlockers && (
          <EmptyActivityBlockers
            onCreate={openActivityBlockersDrawer}
            canEditActivityBlockers={canEditActivityBlockers}
          />
        )}

        {hasBlockers && (
          <>
            <ul className="mt-4 text-sm space-y-1 p-1 overflow-y-auto overflow-x-hidden">
              {activeBlockers.map((blocker) => (
                <BlockerListItem
                  key={blocker.id}
                  issue={blocker.issue}
                  projectId={project.id}
                  onRemove={() => {
                    setSelectedBlockerId(blocker.id);
                    openDeleteBlockerConfirmation();
                  }}
                  canEditActivityBlockers={canEditActivityBlockers}
                />
              ))}

              {hasResolvedAndActiveBlockers && (
                <div className="py-0.5">
                  <Divider orientation="horizontal" />
                </div>
              )}

              {resolvedBlockers.map((blocker) => (
                <BlockerListItem
                  key={blocker.id}
                  issue={blocker.issue}
                  projectId={project.id}
                  onRemove={() => {
                    setSelectedBlockerId(blocker.id);
                    openDeleteBlockerConfirmation();
                  }}
                  canEditActivityBlockers={canEditActivityBlockers}
                />
              ))}
            </ul>
            <BlockersSearchField onSelect={onAddBlocker} canEditActivityBlockers={canEditActivityBlockers} />
          </>
        )}
      </div>

      <DeleteActivityBlockerConfirmationModal
        open={deleteBlockerConfirmationOpen}
        onConfirm={() => {
          onDeleteBlocker(selectedBlockerId!);
          closeDeleteBlockerConfirmation();
        }}
        onClose={closeDeleteBlockerConfirmation}
      />
      <ActivityBlockersDrawer
        isOpen={isOpenActivityBlockersDrawer}
        onClose={closeActivityBlockersDrawer}
        shiftActivityId={shiftActivity.id}
      />
    </>
  );
};
