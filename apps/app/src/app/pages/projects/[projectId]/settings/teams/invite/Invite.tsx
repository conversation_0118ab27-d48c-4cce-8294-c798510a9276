import React from 'react';
import { useMessage, useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import Page from '@shape-construction/arch-ui/src/Page';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useQuotaLimits } from 'app/components/SubscriptionPlanFeatures/QuotaLimits/useQuotaLimits';
import { useProjectPeople } from 'app/queries/projects/people';
import { useProjectInvite } from 'app/queries/projects/projects';
import { useCreateTeam } from 'app/queries/projects/teams';
import { useNavigate, useParams } from 'react-router';
import { DisplayNameField, type DisplayNameFieldProps } from '../components/DisplayNameField/DisplayNameField';
import { PeopleList } from '../components/PeopleList/PeopleList';
import { QuickAddUser, type QuickAddUserProps } from '../components/QuickAddUser/QuickAddUser';

type Params = {
  projectId: string;
};

export const Invite = () => {
  const navigate = useNavigate();
  const { projectId } = useParams() as Params;
  const messages = useMessageGetter('admin.teams.invite');
  const backNavigationTitle = useMessage('navigation.backTo', { route: 'Organisations' });
  const createTeam = useCreateTeam();
  const lastCreatedTeam = createTeam.data;
  const { data: teamMembers } = useProjectPeople(
    projectId,
    { team_id: lastCreatedTeam?.id },
    { query: { enabled: Boolean(lastCreatedTeam) } }
  );
  const projectInvite = useProjectInvite();
  const { quotaLimits, isLoading } = useQuotaLimits('usersPerTeam', {
    teamId: lastCreatedTeam?.id,
    disabled: !lastCreatedTeam,
  });

  const handleCreate: DisplayNameFieldProps['onSubmit'] = (values) => {
    createTeam.mutate({ projectId, data: { display_name: values.displayName } });
  };

  const handleInviteUser: QuickAddUserProps['onInviteUser'] = (values) => {
    if (!lastCreatedTeam) return;

    projectInvite.mutate({
      projectId,
      teamId: lastCreatedTeam.id,
      data: { email: values.email, role: 'owner' },
    });
  };

  const resetForm = () => {
    createTeam.reset();
  };

  const redirectToOrg = () => {
    if (lastCreatedTeam?.id) navigate(`/projects/${projectId}/settings/teams/${lastCreatedTeam.id}`);
  };

  const redirectToTeams = () => {
    navigate(`/projects/${projectId}/settings/teams`);
  };

  return (
    <Page>
      <Page.Header
        title={messages('header')}
        backNavigationTitle={backNavigationTitle}
        hasBackNavigation
        onBackNavigation={redirectToTeams}
      />
      <Page.Body className="bg-white">
        <h6 className={cn('pb-2 text-xl font-medium', lastCreatedTeam?.id ? 'text-gray-500' : 'text-indigo-500')}>
          {messages('step1')}
        </h6>

        {lastCreatedTeam?.id ? (
          <span className="text-xl font-medium">{lastCreatedTeam.displayName}</span>
        ) : (
          <div className="space-y-4">
            <span className="text-base">{messages('nameLabel')}</span>
            <DisplayNameField
              defaultValue=""
              onSubmit={handleCreate}
              loading={createTeam.isPending}
              submitButtonTitle={messages('actions.create')}
            />
          </div>
        )}

        <hr className="my-4" />

        <h6 className={cn('pb-2 text-xl font-medium', lastCreatedTeam?.id ? 'text-indigo-500' : 'text-gray-400')}>
          {messages('step2')}
        </h6>

        <div hidden={!lastCreatedTeam?.id} className="space-y-4 pb-2">
          <span className="text-base">{messages('emailLabel')}</span>
          <QuickAddUser
            onInviteUser={handleInviteUser}
            disabled={isLoading}
            teamSize={
              quotaLimits && {
                maximum: quotaLimits.maximum,
                current: teamMembers?.length || 0,
              }
            }
          />
          {teamMembers ? <PeopleList people={teamMembers} /> : null}
        </div>
        <div className="flex justify-end gap-2">
          {lastCreatedTeam?.id && (
            <Button color="secondary" variant="outlined" size="md" onClick={resetForm}>
              {messages('actions.add')}
            </Button>
          )}
          <Button color="primary" variant="contained" size="md" onClick={redirectToOrg} disabled={!lastCreatedTeam?.id}>
            {messages('actions.done')}
          </Button>
        </div>
      </Page.Body>
    </Page>
  );
};

export { Invite as Component };
