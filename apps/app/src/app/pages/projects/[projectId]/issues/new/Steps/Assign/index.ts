import type { IssueSchema } from '@shape-construction/api/src/types';
import type { FormikConfig } from 'formik';
import messages from 'messages';
import * as Yup from 'yup';
import { Assign, type AssignFormValues } from './Assign';

export const validationSchema = Yup.object().shape({
  draftAssigneeId: Yup.string().required(messages.errors.requiredField()),
});

type GenerateFormPropsType = (initialValues: { initialValues: IssueSchema }) => Partial<FormikConfig<AssignFormValues>>;

export const generateFormProps: GenerateFormPropsType = ({ initialValues = { draftAssigneeId: '' } }) => ({
  initialValues: {
    draftAssigneeId: initialValues.draftAssigneeId || '',
  },
  validationSchema,
});

export default Assign;
