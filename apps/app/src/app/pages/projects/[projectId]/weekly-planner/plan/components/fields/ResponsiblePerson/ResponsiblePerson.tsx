import React, { useMemo, useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { TeamMemberSchema, UserBasicDetailsSchema } from '@shape-construction/api/src/types';
import { UserAvatar } from '@shape-construction/arch-ui/src/Avatar';
import * as Select from '@shape-construction/arch-ui/src/Select';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useQuery } from '@tanstack/react-query';
import { useConstructionRoles } from 'app/components/People/constructionRoles/hooks/useConstructionRoles';
import { PersonItem } from 'app/components/PersonItem/PersonItem';
import { NoSearchResults } from 'app/components/Search/NoSearchResults';
import { SearchLoading } from 'app/components/Search/SearchLoading';
import { useCurrentProject } from 'app/contexts/currentProject';
import { getProjectPersonQueryOptions, useProjectPeople } from 'app/queries/projects/people';
import { useCurrentUser } from 'app/queries/users/users';
import type { FieldProps } from '../types';

export const SelectedUser: React.FC<{ user?: UserBasicDetailsSchema }> = ({ user }) => {
  const messages = useMessageGetter('weeklyPlanner.workPlans.planEditor.responsiblePerson');

  return (
    <div className="flex items-center gap-x-2">
      <UserAvatar user={user} size="sm" />
      <p className="text-sm truncate">
        {user ? <span>{user.name}</span> : <span className="text-gray-400">{messages('empty')}</span>}
      </p>
    </div>
  );
};

type ResponsiblePersonProps = Omit<FieldProps, 'onUpdateActivity'>;

export const ResponsiblePerson: React.FC<ResponsiblePersonProps> = ({
  planActivity,
  onUpdateShiftActivity,
  isEditable,
}) => {
  const currentUser = useCurrentUser();
  const messages = useMessageGetter('weeklyPlanner.workPlans.planEditor.responsiblePerson');
  const meMessage = useMessageGetter('search.people');

  const [selectedTeamMemberId, setSelectedTeamMemberId] = useState<null | number>(null);
  const [personTerm, setPersonTerm] = useState('');

  const project = useCurrentProject();
  const { data: searchPeople, isLoading } = useProjectPeople(project.id, { search: personTerm });
  const { constructionRoles, isLoading: isLoadingRoles } = useConstructionRoles();
  const { data: selectedPerson } = useQuery(
    getProjectPersonQueryOptions(project.id, planActivity?.shiftActivity.assignedTeamMemberId! || selectedTeamMemberId!)
  );

  const handleOnChange = (value: TeamMemberSchema['id']) => {
    setSelectedTeamMemberId(value);
    onUpdateShiftActivity({
      assigned_team_member_id: value,
    });
  };

  const displayedUser = useMemo(() => {
    return searchPeople?.find((person) => person.id === selectedTeamMemberId)?.user || selectedPerson?.user;
  }, [searchPeople, selectedTeamMemberId, selectedPerson]);

  const renderContent = () => {
    if (isLoading || isLoadingRoles) {
      return (
        <Select.PanelSection className="flex-1 w-full flex flex-col justify-center">
          <SearchLoading />
        </Select.PanelSection>
      );
    }

    if (searchPeople?.length === 0) {
      return (
        <Select.PanelSection className="flex-1 w-full flex flex-col items-center justify-center">
          <NoSearchResults />
        </Select.PanelSection>
      );
    }

    return searchPeople?.map((person) => (
      <Select.Option key={person.id} value={person.id} className="md:min-h-14">
        <PersonItem
          avatar={<UserAvatar user={person.user} size="md" />}
          primaryLabel={person.user.name}
          secondaryLabel={person.team.displayName || ''}
          inlineLabel={person.user.id === currentUser?.id ? meMessage('currentUser') : undefined}
          inlineBadge={person.constructionRole ? constructionRoles[person.constructionRole].label : null}
        />
      </Select.Option>
    ));
  };

  if (!isEditable) {
    return (
      <div className="py-1.5 px-1">
        <SelectedUser user={displayedUser} />
      </div>
    );
  }

  return (
    <Select.Root value={selectedTeamMemberId} onChange={handleOnChange} onClose={() => setPersonTerm('')}>
      <Select.Trigger
        as="div"
        variant="plain"
        size="sm"
        className={cn(
          'group hover:text-gray-800 hover:ring-2 hover:ring-gray-400 focus:ring-2 focus:ring-indigo-500 active:ring-2 active:ring-indigo-500',
          'bg-transparent focus:bg-white hover:bg-white'
        )}
        showChevronOnHover
      >
        <SelectedUser user={displayedUser} />
      </Select.Trigger>
      <Select.ResponsivePanel className="md:min-w-[340px] md:min-h-[310px] md:h-[40vh] h-[80vh]">
        <Select.PanelSection className="px-4 py-5 md:hidden">
          <h1 className="text-base leading-6 font-medium">{messages('search')}</h1>
        </Select.PanelSection>

        <Select.PanelSection>
          <Select.Search placeholder={messages('search')} onChange={(event) => setPersonTerm(event.target.value)} />
        </Select.PanelSection>
        <Select.Options className="flex-1">{renderContent()}</Select.Options>
      </Select.ResponsivePanel>
    </Select.Root>
  );
};
