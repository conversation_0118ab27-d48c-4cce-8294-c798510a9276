// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { NotificationParametersNewShiftReportCommentSchema } from '../types/notificationParametersNewShiftReportCommentSchema';
import { createNotificationActor } from './createNotificationActor';
import { faker } from '@faker-js/faker';

export function createNotificationParametersNewShiftReportComment(
  data?: Partial<NotificationParametersNewShiftReportCommentSchema>
): NotificationParametersNewShiftReportCommentSchema {
  faker.seed([100]);
  return {
    ...{
      type: faker.string.alpha(),
      actor: createNotificationActor(),
      params: {
        channel: faker.helpers.arrayElement<
          NonNullable<NonNullable<NotificationParametersNewShiftReportCommentSchema>['params']>['channel']
        >(['public', 'collaborators']),
        commentId: faker.string.uuid(),
        message: faker.string.alpha(),
        projectId: faker.string.uuid(),
        shiftReportDate: faker.date.anytime().toISOString().substring(0, 10),
        shiftReportId: faker.string.uuid(),
        shiftReportTitle: faker.string.alpha(),
      },
    },
    ...(data || {}),
  };
}
