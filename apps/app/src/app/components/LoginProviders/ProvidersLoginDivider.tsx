import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Divider from '@shape-construction/arch-ui/src/Divider';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { MicrosoftLoginButton } from 'app/components/LoginProviders/Microsoft/MicrosoftLoginButton';
import { GoogleLoginButton } from './Google/GoogleLoginButton';

export interface ProvidersLoginDividerProps {
  className?: string;
}

export const ProvidersLoginDivider = ({ className }: ProvidersLoginDividerProps) => {
  const messages = useMessageGetter('auth');

  return (
    <div className={cn('flex flex-col mt-3 space-y-3 items-center', className)}>
      <Divider orientation="horizontal" variant="middle">
        <span className="uppercase">{messages('or')}</span>
      </Divider>
      <GoogleLoginButton />
      <MicrosoftLoginButton />
    </div>
  );
};
