import React, { useMemo } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ShiftActivityOverviewWeeklyPlanningEntrySchema } from '@shape-construction/api/src/types';
import Button from '@shape-construction/arch-ui/src/Button';
import { ArrowsPointingOutIcon, ArrowTopRightOnSquareIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { parseDate } from '@shape-construction/utils/DateTime';
import { Link } from 'app/components/UI/Link/Link';
import { useActivityInsightsWeeklyPlanning } from 'app/queries/activities/activities';
import { useProjectPeople } from 'app/queries/projects/people';
import { type Params, useNavigate, useParams } from 'react-router';
import { getVarianceCategoryLabels } from '../weekly-planner/utils.ts';
import { AuthorCell } from './components/AuthorCell';
import { Widget } from './components/Widget';
import { WidgetTable } from './components/WidgetTable';
import { trimWidgetEntryList } from './utils';

type WeeklyPlanningWidgetProps = {
  expanded?: boolean;
};

export const WeeklyPlanningWidget: React.FC<WeeklyPlanningWidgetProps> = ({ expanded }) => {
  const { projectId, shiftActivityId } = useParams<Params>() as Params;
  const messages = useMessageGetter('shiftManager.activities.weeklyPlanning');
  const varianceCategoryMessages = useMessageGetter('weeklyPlanner.workPlans.lookback.varianceCategory.options');
  const varianceCategoryLabel = useMemo(() => {
    const mappedCategories = getVarianceCategoryLabels(varianceCategoryMessages);
    return (category: string | null) => (category ? (mappedCategories.get(category) ?? '-') : '-');
  }, [varianceCategoryMessages]);
  const { isError, isLoading, data } = useActivityInsightsWeeklyPlanning(projectId!, shiftActivityId!);
  const navigate = useNavigate();
  const { data: projectPeople } = useProjectPeople(projectId!);

  const columns = [
    {
      key: 'startDate',
      label: messages('tableHeaders.duration'),

      render: (row: ShiftActivityOverviewWeeklyPlanningEntrySchema) => {
        return `${parseDate(row.startDate).format('DD-MMM-YYYY')} - ${parseDate(row.endDate).format('DD-MMM-YYYY')}`;
      },
    },
    {
      key: 'expectedPercentageCompleted',
      label: messages('tableHeaders.expectedProgress'),
      render: (row: ShiftActivityOverviewWeeklyPlanningEntrySchema) => {
        return row.expectedPercentageCompleted ? `${row.expectedPercentageCompleted}%` : '-';
      },
    },
    {
      key: 'actualPercentageCompleted',
      label: messages('tableHeaders.actualProgress'),
      render: (row: ShiftActivityOverviewWeeklyPlanningEntrySchema) => {
        return row.actualPercentageCompleted ? `${row.actualPercentageCompleted}%` : '-';
      },
    },
    {
      key: 'varianceCategory',
      label: messages('tableHeaders.varianceCategory'),
      render: (row: ShiftActivityOverviewWeeklyPlanningEntrySchema) => {
        return varianceCategoryLabel(row.varianceCategory);
      },
    },
    {
      key: 'varianceRemarks',
      label: messages('tableHeaders.remarks'),
      showOnExpandOnly: true,
      render: (row: ShiftActivityOverviewWeeklyPlanningEntrySchema) => {
        return row.varianceRemarks ?? '-';
      },
    },
    {
      key: 'mitigationMeasures',
      label: messages('tableHeaders.mitigation'),
      showOnExpandOnly: true,
      render: (row: ShiftActivityOverviewWeeklyPlanningEntrySchema) => {
        return row.mitigationMeasures ?? '-';
      },
    },
    {
      key: 'authorId',
      label: messages('tableHeaders.author'),
      showOnExpandOnly: true,
      render: (row: ShiftActivityOverviewWeeklyPlanningEntrySchema) => {
        const projectPerson = projectPeople?.find((person) => person.id === row.authorId);
        return <AuthorCell user={projectPerson?.user} />;
      },
    },
    {
      key: 'link',
      label: '',
      render: (row: ShiftActivityOverviewWeeklyPlanningEntrySchema) => {
        return (
          <Tooltip.Root>
            <Tooltip.Trigger className="flex flex-row align-center gap-2">
              <Link
                target="_blank"
                rel="noopener noreferrer"
                aria-label={`link-to-activity-${row.weeklyWorkPlanId}`}
                to={`/projects/${projectId}/weekly-planner/plans/${row.weeklyWorkPlanId}`}
              >
                <ArrowTopRightOnSquareIcon className="h-5 w-5" />
              </Link>
            </Tooltip.Trigger>
            <Tooltip.Content side="bottom">
              {messages('tooltip.weeklyWorkPlanLink', { planName: row.weeklyWorkPlanTitle })}
            </Tooltip.Content>
          </Tooltip.Root>
        );
      },
    },
  ];

  return (
    <Widget
      actions={
        !expanded && (
          <Button
            aria-label={messages('actions.seeAll')}
            size="xxs"
            color="secondary"
            variant="outlined"
            leadingIcon={ArrowsPointingOutIcon}
            onClick={() => {
              navigate(`/projects/${projectId}/activities/${shiftActivityId}/weekly-planning`);
            }}
          >
            {messages('actions.seeAll')}
          </Button>
        )
      }
      title={messages('title')}
      isLoading={isLoading}
      isError={isError}
    >
      <WidgetTable<ShiftActivityOverviewWeeklyPlanningEntrySchema>
        data={expanded ? data?.entries : trimWidgetEntryList(data?.entries)}
        columns={expanded ? columns : columns.filter((column) => !column.showOnExpandOnly)}
        emptyStateLabels={{
          title: messages('emptyStateLabels.title'),
          subtitle: messages('emptyStateLabels.subtitle'),
        }}
      />
    </Widget>
  );
};
