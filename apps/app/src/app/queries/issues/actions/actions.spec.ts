import { issueFactory } from '@shape-construction/api/factories/issues';
import { teamFactory } from '@shape-construction/api/factories/teams';
import { userBasicDetailsFactory } from '@shape-construction/api/factories/users';
import {
  postApiProjectsProjectIdIssuesIssueIdApproveMockHandler,
  postApiProjectsProjectIdIssuesIssueIdReopenMockHandler,
} from '@shape-construction/api/handlers-factories/projects/issues';
import * as toasts from '@shape-construction/arch-ui/src/Toast/toasts';
import { server } from 'tests/mock-server';
import { act, renderHook, waitFor } from 'tests/test-utils';
import { useIssueAction } from './actions';

describe('useIssueAction', () => {
  describe('when resolving an issue', () => {
    describe('and the next actioner is not the last approver', () => {
      it('display the message for next approver', async () => {
        const spyOnToast = jest.spyOn(toasts, 'showSuccessToast');
        const issue = issueFactory({
          id: 'workflow-issue',
          nextActionerId: 0,
          nextActionerUser: userBasicDetailsFactory({ name: 'shape one' }),
          approvers: [
            {
              approvedAt: null,
              id: '0',
              sortOrder: 0,
              status: 'pending',
              teamMemberId: 0,
              user: userBasicDetailsFactory({ name: 'shape one' }),
              team: teamFactory({ id: 'team-0', displayName: 'Team A' }),
            },
            {
              approvedAt: null,
              id: '1',
              sortOrder: 0,
              status: 'pending',
              teamMemberId: 1,
              user: userBasicDetailsFactory({ name: 'shape two' }),
              team: teamFactory({ id: 'team-0', displayName: 'Team A' }),
            },
          ],
        });
        server.use(postApiProjectsProjectIdIssuesIssueIdApproveMockHandler(() => issue));

        const { result } = renderHook(() => useIssueAction());

        act(() =>
          result.current.mutate({
            projectId: issue.projectId,
            issueId: issue.id,
            action: 'approve',
          })
        );

        await waitFor(() =>
          expect(spyOnToast).toHaveBeenCalledWith({
            message: 'issue.detail.actions.approve.inProgress',
          })
        );
      });
    });

    describe('and the next actioner is the last approver', () => {
      it('display the message for final approver', async () => {
        const spyOnToast = jest.spyOn(toasts, 'showSuccessToast');
        const issue = issueFactory({
          id: 'workflow-issue',
          nextActionerId: 0,
          nextActionerUser: userBasicDetailsFactory({ name: 'shape one' }),
          approvers: [
            {
              approvedAt: null,
              id: '0',
              sortOrder: 0,
              status: 'pending',
              teamMemberId: 0,
              user: userBasicDetailsFactory({ name: 'shape one' }),
              team: teamFactory({ id: 'team-0', displayName: 'Team A' }),
            },
          ],
        });
        server.use(postApiProjectsProjectIdIssuesIssueIdApproveMockHandler(() => issue));

        const { result } = renderHook(() => useIssueAction());

        act(() =>
          result.current.mutate({
            projectId: issue.projectId,
            issueId: issue.id,
            action: 'approve',
          })
        );

        await waitFor(() =>
          expect(spyOnToast).toHaveBeenCalledWith({
            message: 'issue.detail.actions.approve.final',
          })
        );
      });
    });

    describe('and there are no more pending approvers', () => {
      it('displays the message for the issue closed', async () => {
        const spyOnToast = jest.spyOn(toasts, 'showSuccessToast');
        const issue = issueFactory({
          id: 'workflow-issue',
          nextActionerId: null,
          nextActionerUser: null,
          approvers: [],
        });
        server.use(postApiProjectsProjectIdIssuesIssueIdApproveMockHandler(() => issue, undefined, { once: true }));

        const { result } = renderHook(() => useIssueAction());

        act(() =>
          result.current.mutate({
            projectId: issue.projectId,
            issueId: issue.id,
            action: 'approve',
          })
        );

        await waitFor(() =>
          expect(spyOnToast).toHaveBeenCalledWith({
            message: 'issue.detail.actions.approve.closed',
          })
        );
      });
    });
  });

  describe('when an issue is reopened', () => {
    it('display the message for issue reopened', async () => {
      const spyOnToast = jest.spyOn(toasts, 'showSuccessToast');
      const issue = issueFactory({
        currentState: 'assigned',
      });
      server.use(postApiProjectsProjectIdIssuesIssueIdReopenMockHandler(() => issue));

      const { result } = renderHook(() => useIssueAction());

      act(() =>
        result.current.mutate({
          projectId: issue.projectId,
          issueId: issue.id,
          action: 'reopen',
        })
      );

      await waitFor(() =>
        expect(spyOnToast).toHaveBeenCalledWith({
          message: 'issue.detail.actions.reopen',
        })
      );
    });
  });
});
