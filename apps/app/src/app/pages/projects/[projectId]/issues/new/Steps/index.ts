import { useState } from 'react';
import { useMessage } from '@messageformat/react';
import * as assignModule from './Assign';
import * as categoryModule from './Category';
import * as disciplineModule from './Discipline';
import * as dueDateModule from './DueDate';
import * as locationModule from './Location';
import * as titleAndDescriptionModule from './TitleAndDescription';

export const stepModules = [
  titleAndDescriptionModule,
  dueDateModule,
  categoryModule,
  disciplineModule,
  locationModule,
  assignModule,
];

const stepsWithMessages = [
  'issue.new.titleAndDescription.description',
  'issue.new.dueDate.description',
  'issue.new.category.description',
  'issue.new.discipline.description',
  'issue.new.location.description',
  'issue.new.assign.description',
];

export const LAST_STEP_IDX = stepModules.length - 1;

export const useSteps = (initialStep: number) => {
  const [stepNo, setStepNo] = useState(initialStep);
  const currStepModule = stepModules[stepNo];
  const stepDescription = useMessage(stepsWithMessages[stepNo]);

  const goToPreviousStep = () => {
    if (stepNo > 0) setStepNo((currStepNo) => currStepNo - 1);
  };
  const goToNextStep = () => {
    if (stepNo < LAST_STEP_IDX) {
      setStepNo((currStepNo) => currStepNo + 1);
    } else {
      return { reachedLastStep: true };
    }
    return {};
  };

  return {
    lastStepNo: LAST_STEP_IDX + 1,
    currStepNo: stepNo + 1,
    currStep: { ...currStepModule, stepDescription },
    goToPreviousStep,
    goToNextStep,
  };
};
