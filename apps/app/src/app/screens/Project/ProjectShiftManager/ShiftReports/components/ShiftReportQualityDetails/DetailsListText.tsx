import React from 'react';
import { CircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { CheckCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';

type CheckedRowProps = { isChecked: boolean; isDeactivated?: boolean };

const CheckedRow: React.FC<CheckedRowProps> = ({ isChecked, isDeactivated }) =>
  isChecked ? (
    <CheckCircleIcon
      aria-label="checked progress item"
      className={cn('w-5 h-5', {
        'text-green-500': !isDeactivated,
        'text-gray-500': isDeactivated,
      })}
    />
  ) : (
    <CircleIcon aria-label="unchecked progress item" className="w-5 h-5 text-gray-400" />
  );

export type DetailsListTextProps = {
  text: string;
  isComplete?: boolean;
  isDeactivated?: boolean;
};

export const DetailsListText: React.FC<DetailsListTextProps> = ({ text, isComplete, isDeactivated }) => {
  return (
    <div
      className={cn('flex py-2 justify-between items-center', {
        'opacity-50': isDeactivated,
      })}
    >
      <div
        className={cn('text-sm leading-5 font-medium text-gray-700', {
          'opacity-50': isComplete,
        })}
      >
        {text}
      </div>
      {isComplete !== undefined && <CheckedRow isChecked={isComplete} isDeactivated={isDeactivated} />}
    </div>
  );
};
