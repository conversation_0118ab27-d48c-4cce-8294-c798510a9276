import React, { forwardRef, useEffect, useState } from 'react';
import type { ProjectSchema, TeamSchema } from '@shape-construction/api/src/types';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import { InputAdornment } from '@shape-construction/arch-ui/src/InputAdornment';
import type { SelectRootProps } from '@shape-construction/arch-ui/src/Select';
import * as Select from '@shape-construction/arch-ui/src/Select';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useDebounceCallback } from '@shape-construction/hooks';
import { useQuery } from '@tanstack/react-query';
import { NoSearchResults } from 'app/components/Search/NoSearchResults';
import { SearchLoading } from 'app/components/Search/SearchLoading';
import { getProjectTeamsQueryOptions } from 'app/queries/projects/teams';
import { useParams } from 'react-router';

type Params = { projectId: ProjectSchema['id'] };
type ResponsibleTeamSelectProps = SelectRootProps<TeamSchema['id'][], true> & {
  triggerLabel: string;
  filterByLabel: string;
  searchPlaceholder: string;
};

const initialValue: string[] = [];

export const ResponsibleTeamSelect = forwardRef<React.ElementRef<typeof Select.Root>, ResponsibleTeamSelectProps>(
  ({ value = initialValue, onChange, triggerLabel, filterByLabel, searchPlaceholder }, ref) => {
    const { projectId } = useParams() as Params;
    const [checked, setChecked] = useState(value);
    const { data: teams, isLoading } = useQuery(getProjectTeamsQueryOptions(projectId));
    const { data: selectedTeams = [] } = useQuery({
      ...getProjectTeamsQueryOptions(projectId),
      select: (data) => {
        return checked.map((teamId) => data?.find(({ id }) => teamId === String(id))!) || [];
      },
    });

    const [filteredTeams, setFilteredTeams] = useState(teams);

    const handleSearch = useDebounceCallback((searchTerm: string) => {
      const filteredData = teams?.filter((team) => team?.displayName?.toLowerCase().includes(searchTerm.toLowerCase()));
      setFilteredTeams(filteredData);
    }, 250);

    const handleOnChange = (values: string[]) => {
      onChange?.(values.map(String));
    };

    const handleOnRemove = (team: TeamSchema) => {
      setChecked((state) => state.filter((teamId) => teamId !== String(team.id)));
      handleOnChange(checked.filter((teamId) => teamId !== String(team.id)).map(String));
    };

    const renderContent = () => {
      if (isLoading) {
        return (
          <Select.PanelSection className="flex-1 w-full flex flex-col justify-center">
            <SearchLoading />
          </Select.PanelSection>
        );
      }

      if (!filteredTeams || filteredTeams?.length === 0) {
        return (
          <Select.PanelSection className="flex-1 w-full flex flex-col items-center justify-center">
            <NoSearchResults />
          </Select.PanelSection>
        );
      }

      return filteredTeams?.map((team) => (
        <Select.Option key={team.id} value={team.id}>
          <div className="flex items-center gap-x-2" key={team?.id}>
            <Avatar size="md" text={team?.displayName || ''} />
            <span className="text-sm font-medium leading-5 text-gray-900">{team?.displayName}</span>
          </div>
        </Select.Option>
      ));
    };

    useEffect(() => {
      if (value) setChecked(value);
    }, [value]);

    useEffect(() => {
      if (teams) setFilteredTeams(teams);
    }, [teams]);

    return (
      <Select.Root
        ref={ref}
        multiple
        by={(a, b) => String(a) === String(b)}
        value={checked}
        onChange={handleOnChange}
        onClose={() => setFilteredTeams(teams)}
      >
        <Select.Trigger
          as="div"
          variant="bordered"
          size="sm"
          startAdornment={<InputAdornment>{triggerLabel}</InputAdornment>}
        >
          <div className="text-right">
            <Select.MultipleValue value={value} condensed />
          </div>
        </Select.Trigger>

        <Select.ResponsivePanel className="md:min-w-[340px] h-[60vh] md:h-[80vh]">
          <Select.PanelSection className="px-4 py-5 md:hidden">
            <h1 className="text-base leading-6 font-medium">{filterByLabel}</h1>
          </Select.PanelSection>

          <Select.PanelSection className={cn('max-h-[60px] overflow-y-auto', { hidden: checked.length === 0 })}>
            <Select.MultipleValue
              value={selectedTeams}
              onRemove={(team) => handleOnRemove(team)}
              onRemoveAll={() => {
                setChecked([]);
                handleOnChange([]);
              }}
              displayValue={(team) => team?.displayName!}
            />
          </Select.PanelSection>

          <Select.PanelSection>
            <Select.Search placeholder={searchPlaceholder} onChange={(event) => handleSearch(event.target.value)} />
          </Select.PanelSection>
          <Select.Options className="flex-1">{renderContent()}</Select.Options>
        </Select.ResponsivePanel>
      </Select.Root>
    );
  }
);

ResponsibleTeamSelect.displayName = 'ResponsibleTeamSelect';
