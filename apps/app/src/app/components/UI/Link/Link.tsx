import React, { type ComponentProps } from 'react';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { Link as ReactRouterLink } from 'react-router';

/**
 * This component extends React router dom Link component, adding style to it.
 */

export type LinkProps = ComponentProps<typeof ReactRouterLink>;

export function Link(props: LinkProps) {
  const newClassName = cn('font-medium text-indigo-500 hover:text-indigo-400', props.className);

  return <ReactRouterLink {...props} className={newClassName} />;
}
