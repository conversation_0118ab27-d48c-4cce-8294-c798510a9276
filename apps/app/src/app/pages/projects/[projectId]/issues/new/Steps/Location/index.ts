import type { IssueSchema } from '@shape-construction/api/src/types';
import type { FormikConfig } from 'formik';
import * as Yup from 'yup';
import { Location, type LocationFormValues } from './Location';

export const validationSchema = Yup.object().shape({
  locationId: Yup.string(),
});

type GenerateFormPropsType = (initialValues: {
  initialValues: IssueSchema;
}) => Partial<FormikConfig<LocationFormValues>>;

export const generateFormProps: GenerateFormPropsType = ({ initialValues = { locationId: '' } }) => ({
  initialValues: {
    locationId: initialValues.locationId || '',
  },
  validationSchema,
});

export default Location;
