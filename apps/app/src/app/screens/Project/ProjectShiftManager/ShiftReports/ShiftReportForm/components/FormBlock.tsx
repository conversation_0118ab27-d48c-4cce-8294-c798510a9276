import React, { useCallback } from 'react';
import { useMessageGetter } from '@messageformat/react';
import AvatarGroupStacked from '@shape-construction/arch-ui/src/Avatar/AvatarGroupStacked';
import Badge from '@shape-construction/arch-ui/src/Badge';
import { type BadgeProps, SHAPE, SIZE } from '@shape-construction/arch-ui/src/Badge/Badge.types';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import CollapsableCard from '@shape-construction/arch-ui/src/CollapsableCard';
import { TrashIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import type { User } from '@shape-construction/arch-ui/src/types/User';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useMediaQuery } from '@shape-construction/hooks';
import type { UseFieldArrayRemove } from 'react-hook-form';
import type { Maybe } from 'yup/lib/types';
import {
  ShiftReportDocumentPickerLineItems,
  type ShiftReportDocumentPickerLineItemsProps,
} from '../../components/ShiftReportDocuments/components/ShiftReportDocumentPickerLineIItems';
import type { ShiftReportRowPathname, ShiftReportsItemWithExtraFields } from '../ShiftReportForm';

export type FormBlockProps = {
  children: React.ReactNode;
  rowIndex: number;
  user?: User;
  onDelete: UseFieldArrayRemove;
  deleteIcon?: React.ElementType;
  className?: string;
  title: Maybe<string>;
  subHeader?: React.ReactNode;
  defaultTitle?: string;
  badges?: Array<Partial<BadgeProps> & { label: string }>;
  shouldAutofocus?: boolean;
  rowId?: string | null;
  rowType?: string;
  actions?: React.ReactNode;
  resource: ShiftReportsItemWithExtraFields;
  path: ShiftReportRowPathname;
  disabled?: boolean;
};

export const FormBlock: React.FC<FormBlockProps> = ({
  rowIndex,
  user,
  onDelete,
  children,
  deleteIcon: DeleteIcon,
  title,
  subHeader,
  defaultTitle,
  badges,
  actions,
  shouldAutofocus = false,
  rowId,
  rowType,
  resource,
  path,
  disabled,
}) => {
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const isOpenByDefault = isLargeScreen || shouldAutofocus;

  const messages = useMessageGetter('shiftReport.form');
  const getHighlightColor = useCallback(() => 'neutral', []);

  const handleDeleteItem = (index: number) => {
    onDelete(index);
  };

  return (
    <CollapsableCard className="w-full bg-gray-50" openByDefault={isOpenByDefault}>
      {({ isOpen }) => (
        <>
          <CollapsableCard.Header
            isOpen={isOpen}
            aria-label={isOpen ? messages('closeRowLinks') : messages('openRowLinks')}
            className="text-left"
          >
            {isOpen ? (
              <div className="flex justify-end items-center w-full h-0 gap-x-2">
                <AvatarGroupStacked size="sm" getHighlightColor={getHighlightColor} users={[user]} />
              </div>
            ) : (
              <div className={cn({ 'text-gray-500': !title })}>{title || defaultTitle}</div>
            )}
          </CollapsableCard.Header>
          <CollapsableCard.Actions>
            {isOpen && (
              <>
                {actions}
                <ShiftReportDocumentPickerLineItems
                  resourceId={rowId!}
                  resourceType={rowType as ShiftReportDocumentPickerLineItemsProps['resourceType']}
                  rowIndex={rowIndex}
                  resource={resource}
                  path={path}
                />
                <IconButton
                  aria-label={messages('delete')}
                  color="secondary"
                  size="md"
                  variant="text"
                  icon={DeleteIcon || TrashIcon}
                  onClick={() => handleDeleteItem(rowIndex)}
                />
              </>
            )}
          </CollapsableCard.Actions>
          <CollapsableCard.Subheader>
            {!isOpen && (
              <div className="pt-2 flex gap-2 items-end">
                {subHeader && <div>{subHeader}</div>}

                {badges && badges.length !== 0 && (
                  <div className="flex items-center gap-2 ">
                    {badges?.map((badge) => (
                      <Badge key={badge.label} size={SIZE.SMALL} shape={SHAPE.BASIC} {...badge} />
                    ))}
                  </div>
                )}
              </div>
            )}
          </CollapsableCard.Subheader>
          <CollapsableCard.Content className="pt-0">
            <fieldset disabled={disabled} className="disabled:opacity-50">
              {children}
            </fieldset>
          </CollapsableCard.Content>
        </>
      )}
    </CollapsableCard>
  );
};
