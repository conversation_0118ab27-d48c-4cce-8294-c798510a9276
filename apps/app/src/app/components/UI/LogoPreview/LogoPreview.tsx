import React, { useMemo } from 'react';
import { PhotoIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';

interface LogoPreviewProps {
  className?: string;
  url?: string;
  logo?: File;
}

export const LogoPreview = ({ logo, url, className }: LogoPreviewProps) => {
  const thumbnail = useMemo(() => (logo && logo instanceof File ? URL.createObjectURL(logo) : ''), [logo]);
  const hasPreview = !!thumbnail || !!url;
  const previewSrc = thumbnail || url;

  return (
    <div className={cn('flex justify-center w-14 h-14 mr-5', className)}>
      {hasPreview ? (
        <div>
          <img src={previewSrc} alt="Thumbnail" className="w-full h-full rounded-lg object-contain" />
        </div>
      ) : (
        <div className="w-full h-full rounded-lg bg-gray-50 border flex justify-center items-center transition">
          <PhotoIcon className="w-6 text-gray-300" />
        </div>
      )}
    </div>
  );
};
