import { useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import { ShapeIssueTrackerIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { PlusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';

type EmptyActivityBlockersProps = {
  onCreate: () => void;
  canEditActivityBlockers: boolean;
};

export const EmptyActivityBlockers: React.FC<EmptyActivityBlockersProps> = ({ onCreate, canEditActivityBlockers }) => {
  const messages = useMessageGetter('shiftManager.activities.readiness.blockers');

  return (
    <div className="flex flex-col gap-4 items-center justify-center p-4">
      <ShapeIssueTrackerIcon className="h-12 w-12 text-icon-neutral-subtle" />
      <div className="flex flex-col gap-2 items-center text-center">
        <h1 className="text-base-leading-6-font-medium text-neutral-bold">{messages('empty.title')}</h1>
        <span className="text-sm-leading-5-font-normal text-neutral-subtle">{messages('empty.description')}</span>
      </div>
      <Tooltip.Root>
        <Tooltip.Trigger asChild>
          <Button
            size="md"
            color="primary"
            variant="contained"
            leadingIcon={PlusIcon}
            onClick={onCreate}
            disabled={!canEditActivityBlockers}
            aria-disabled={!canEditActivityBlockers}
          >
            {messages('empty.action')}
          </Button>
        </Tooltip.Trigger>
        <Tooltip.Content side="bottom" hidden={canEditActivityBlockers}>
          {messages('tooltips.noPermission.create')}
        </Tooltip.Content>
      </Tooltip.Root>
    </div>
  );
};
