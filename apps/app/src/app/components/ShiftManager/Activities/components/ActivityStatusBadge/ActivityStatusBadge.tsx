import React from 'react';
import type { ShiftActivitySchema } from '@shape-construction/api/src/types';
import Badge from '@shape-construction/arch-ui/src/Badge';
import { THEME } from '@shape-construction/arch-ui/src/Badge/Badge.types';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import {
  ACTIVITY_STATUS,
  ACTIVITY_STATUS_COLORS,
  ACTIVITY_STATUS_LABELS,
} from 'app/components/ShiftManager/Activities/constants';

interface ActivityStatusBadgeProps {
  status: ShiftActivitySchema['status'];
}

export const ActivityStatusBadge: React.FC<ActivityStatusBadgeProps> = ({ status }) => {
  const newStatus = status === null ? ACTIVITY_STATUS.NOT_STARTED : status;
  if (!newStatus) return null;

  return (
    <div className={cn('whitespace-nowrap')}>
      <Badge
        theme={(ACTIVITY_STATUS_COLORS[newStatus] as THEME) || THEME.GRAY}
        label={ACTIVITY_STATUS_LABELS[newStatus]}
      />
    </div>
  );
};
