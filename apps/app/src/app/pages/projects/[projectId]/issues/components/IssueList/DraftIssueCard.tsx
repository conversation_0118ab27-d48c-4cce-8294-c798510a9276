import React, { useMemo } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { IssueListItemSchema, IssueSchema } from '@shape-construction/api/src/types';
import InputCheckbox from '@shape-construction/arch-ui/src/InputCheckbox';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { formatDate } from '@shape-construction/utils/DateTime';
import IssueCardTitle from 'app/components/IssueCard/components/IssueCardTitle/IssueCardTitle';
import { hasSubCategory } from 'app/constants/IssueCategories';
import { useCurrentProject } from 'app/contexts/currentProject';
import { builderIssue } from 'app/store/issues/issues-action-creators';
import { useDispatch } from 'react-redux';
import { type LinkProps, useLocation, useNavigate } from 'react-router';

export type DraftIssueCardProps = {
  issue: IssueListItemSchema;
  checked: boolean;
  selected: boolean;
  onSelect: (issueId: IssueSchema['id']) => void;
  onUnselect: (issueId: IssueSchema['id']) => void;
};

export function DraftIssueCard({ issue, checked, selected, onSelect, onUnselect }: DraftIssueCardProps) {
  const dispatch = useDispatch();
  const location = useLocation();
  const navigate = useNavigate();
  const { projectId } = issue;
  const project = useCurrentProject();
  const issueDraftMessage = useMessageGetter('issue.draft');

  const toggleSelect = () => {
    if (checked) {
      onUnselect(issue.id);
    } else {
      onSelect(issue.id);
    }
  };

  /**
   * Checks if we have valid category information. We can't go to the details page
   * if we don't know the category as it won't know what fields to show.
   */
  const hasCategory = useMemo(() => {
    if (!issue.category) return false;
    return issue.subCategory || !hasSubCategory(issue.category);
  }, [issue]);

  const getLinkProps = (): LinkProps => {
    if (hasCategory) {
      return {
        to: { search: `?issueId=${issue.id}` },
      };
    }

    return {
      to: `/projects/${projectId}/issues/new`,
      state: { background: location },
    };
  };

  const title = issue.title ? issue.title : issueDraftMessage('title.empty');

  const onClick = () => {
    const { to, state } = getLinkProps();
    dispatch(builderIssue(issue.id));
    navigate(to, { state });
  };

  return (
    <div
      role="listitem"
      aria-label={title}
      className={cn('flex gap-2 mt-2 rounded-md bg-white p-3 shadow-xs', {
        'bg-slate-300': checked,
        'ring-2 ring-indigo-500': selected,
      })}
    >
      <InputCheckbox name="selected" checked={checked} onChange={toggleSelect} />
      <button
        type="button"
        aria-current={selected}
        aria-label={title}
        onClick={onClick}
        className="overflow-hidden min-w-0 flex-1 text-left"
      >
        <div className="flex flex-row gap-3">
          <div className="flex flex-col items-start gap-1">
            <IssueCardTitle content={title} />

            {project?.timezone && (
              <div className="flex items-center gap-4">
                <div className="inline-flex justify-start items-center gap-1">
                  <span className="text-gray-400 text-xs font-medium">{issueDraftMessage('createdAt.title')}:</span>
                  <span className="text-gray-500 text-xs font-medium">
                    {formatDate(issue.createdAt, project.timezone, 'DD-MMM-YYYY')}
                  </span>
                </div>
                <div className="inline-flex justify-start items-center gap-1">
                  <span className="text-gray-400 text-xs font-medium">{issueDraftMessage('updatedAt.title')}:</span>
                  <span className="text-gray-500 text-xs font-medium">
                    {formatDate(issue.updatedAt, project.timezone, 'DD-MMM-YYYY')}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </button>
    </div>
  );
}
