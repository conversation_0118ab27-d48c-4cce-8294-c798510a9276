import React, { useRef } from 'react';
import { useMessageGetter } from '@messageformat/react';
import Table from '@shape-construction/arch-ui/src/Table';
import type { TableHeaderProps } from '@shape-construction/arch-ui/src/Table/components/TableHeader';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { CheckboxCell } from './TableCells/CheckboxTableCell/CheckboxTableCell';

interface CheckboxHeaderProps extends TableHeaderProps {
  handleSelectCheckbox: () => void;
  areAllReportsSelected: boolean;
  isIndeterminateCheckbox?: boolean;
}

export const CheckboxHeader = ({
  handleSelectCheckbox,
  areAllReportsSelected,
  isIndeterminateCheckbox,
  ...restProps
}: CheckboxHeaderProps) => {
  const checkboxMessages = useMessageGetter('shiftReport.list.table.checkbox');

  const checkboxRef = useRef<HTMLInputElement | null>(null);

  return (
    <Table.Header {...restProps} onClick={handleSelectCheckbox}>
      <Tooltip.Root>
        <Tooltip.Trigger asChild>
          <div data-testid="checkbox-tooltip-trigger">
            <CheckboxCell
              ref={checkboxRef}
              onChange={handleSelectCheckbox}
              checked={areAllReportsSelected}
              isIndeterminate={isIndeterminateCheckbox}
              name="selectAllReports"
            />
          </div>
        </Tooltip.Trigger>
        <Tooltip.Content side="top" className="normal-case">
          {areAllReportsSelected ? checkboxMessages('unselectAllOnPage') : checkboxMessages('selectAllOnPage')}
        </Tooltip.Content>
      </Tooltip.Root>
    </Table.Header>
  );
};
