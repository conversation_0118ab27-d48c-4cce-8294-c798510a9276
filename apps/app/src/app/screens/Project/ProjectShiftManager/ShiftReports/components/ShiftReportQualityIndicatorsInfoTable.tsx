import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Badge from '@shape-construction/arch-ui/src/Badge';
import { type BadgeProps, THEME } from '@shape-construction/arch-ui/src/Badge/Badge.types';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import type { ReportQualityLabels } from './getLabelByReportQualityPercentage';

export const qualityLabelBadgeThemeMap: Record<ReportQualityLabels, BadgeProps['theme']> = {
  notuseful: THEME.RED,
  thebasics: THEME.YELLOW,
  good: THEME.BLUE,
  verygood: THEME.GREEN,
  comprehensive: THEME.GREEN_DARK,
};

const qualityLabelBadgeThemeMapEntries = Object.entries(qualityLabelBadgeThemeMap);

export const ShiftReportQualityIndicatorsInfoTable: React.FC = () => {
  const qualityTableInfoMessages = useMessageGetter('shiftReport.qualityTableInfo');
  const qualityLabelMessages = useMessageGetter('shiftReport.qualityLabel');

  return (
    <div className="w-full p-4 space-y-4">
      <div className=" space-y-1">
        <div className="text-base leading-6 font-medium text-gray-800">{qualityTableInfoMessages('title')}</div>
        <p className="text-sm leading-5 font-normal text-gray-700">{qualityTableInfoMessages('info')}</p>
      </div>
      <div className="border border-gray-200 rounded-sm">
        {qualityLabelBadgeThemeMapEntries.map(([slug, theme], index, arr) => {
          const isLast = index === arr.length - 1;

          return (
            <div key={`${slug}-${theme}`} className={cn({ 'border-b border-gray-200': !isLast })}>
              <div className="flex justify-between px-3 py-2 ">
                <Badge label={qualityLabelMessages(slug)} theme={THEME[`${theme!}`]} />
                <span className="text-sx leading-4 font-normal text-gray-500">
                  {qualityTableInfoMessages(`percentageRange.${slug}`)}
                </span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
