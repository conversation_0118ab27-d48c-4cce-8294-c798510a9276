import { validationParams } from 'app/config/validationParams';
import messages from 'messages';
import * as Yup from 'yup';
import { TitleAndDescription } from './TitleAndDescription';

export interface FormValues {
  title: string;
  description: string;
}

export const validationSchema = Yup.object().shape({
  title: Yup.string()
    .max(
      validationParams.issueTitleMaxLength,
      messages.errors.characterLimit({
        fieldName: 'Title',
        maxLength: validationParams.issueTitleMaxLength,
      })
    )
    .required(messages.errors.requiredField()),
  description: Yup.string(),
});

export const generateFormProps = ({ initialValues = {} as FormValues } = {}) => ({
  initialValues: {
    title: initialValues.title || '',
    description: initialValues.description || '',
  },
  validationSchema,
});

export default TitleAndDescription;
