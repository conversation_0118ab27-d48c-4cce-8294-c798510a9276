import messages from 'messages';
import * as Yup from 'yup';
import { Category } from './Category';
import { getSubCateriesDict } from './utils';

export const validationSchema = Yup.object().shape({
  category: Yup.string().required(messages.errors.requiredField()),
  subCategory: Yup.string()
    .nullable()
    .test('required-when-existent', messages.errors.requiredField(), (value, { parent: { category } }) => {
      const selectedCategoryHasSub = !!getSubCategoriesOfSelectedCat(category);
      return !selectedCategoryHasSub || !!value;
    }),
});

export const generateFormProps = ({ initialValues = {} } = {}) => ({
  initialValues: {
    // @ts-expect-error ts-migrate(2339) FIXME: Property 'category' does not exist on type '{}'.
    category: initialValues.category || '',
    // @ts-expect-error ts-migrate(2339) FIXME: Property 'subCategory' does not exist on type '{}'... Remove this comment to see the full error message
    subCategory: initialValues.subCategory || '',
  },
  validationSchema,
});

const { getSubCategoriesOfSelectedCat } = getSubCateriesDict();

export default Category;
