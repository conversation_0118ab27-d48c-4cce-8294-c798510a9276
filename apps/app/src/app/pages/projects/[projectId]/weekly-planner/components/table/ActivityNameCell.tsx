import React from 'react';
import Table from '@shape-construction/arch-ui/src/Table';
import type { TableCellProps } from '@shape-construction/arch-ui/src/Table/components/TableCell';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';

export const ActivityNameCell: React.FC<TableCellProps> = ({ className, ...rest }) => (
  <Table.Cell className={cn(className, 'min-w-40 max-w-[200px]')} {...rest} />
);
