import { formatDate, formatTime, now, setEndOfBusiness } from '@shape-construction/utils/DateTime';
import messages from 'messages';
import * as Yup from 'yup';
import { DueDate } from './DueDate';

export interface FormValues {
  dueDate: string;
  dueDateDate: string;
  dueDateTime: string;
}

export const validationSchema = Yup.object().shape({
  dueDateDate: Yup.string()
    .trim()
    .matches(/^[0-9]{4}-[0-9]{2}-[0-9]{2}$/, messages.errors.invalidDate())
    .required(messages.errors.requiredField()),
  dueDateTime: Yup.string()
    .trim()
    .matches(/^[0-9]{2}:[0-9]{2}$/, messages.errors.invalidTime())
    .required(messages.errors.requiredField()),
});

export const generateFormProps = ({ initialValues = {} as FormValues, timezone = '' } = {}) => ({
  initialValues: {
    dueDateDate: initialValues.dueDate
      ? formatDate(initialValues.dueDate, timezone, 'YYYY-MM-DD')
      : initialValues.dueDateDate,
    dueDateTime: initialValues.dueDate
      ? formatTime(initialValues.dueDate, timezone)
      : initialValues.dueDateTime || setEndOfBusiness(now()).format('HH:mm'),
  },
  validationSchema,
});

export default DueDate;
