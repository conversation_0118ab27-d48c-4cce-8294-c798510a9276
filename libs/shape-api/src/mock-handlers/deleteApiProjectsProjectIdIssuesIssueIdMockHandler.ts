/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdIssuesIssueIdMutationResponseSchema } from '../types/deleteApiProjectsProjectIdIssuesIssueIdSchema';
import { createDeleteApiProjectsProjectIdIssuesIssueIdMutationResponse } from '../factories/createDeleteApiProjectsProjectIdIssuesIssueId';
import { http } from 'msw';

export function deleteApiProjectsProjectIdIssuesIssueIdMockHandler(
  data?:
    | DeleteApiProjectsProjectIdIssuesIssueIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete('/api/projects/:project_id/issues/:issue_id', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createDeleteApiProjectsProjectIdIssuesIssueIdMutationResponse(data)), {
      status: 204,
    });
  });
}
