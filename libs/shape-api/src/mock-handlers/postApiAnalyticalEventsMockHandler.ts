/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiAnalyticalEventsMutationResponseSchema } from '../types/postApiAnalyticalEventsSchema';
import { createPostApiAnalyticalEventsMutationResponse } from '../factories/createPostApiAnalyticalEvents';
import { http } from 'msw';

export function postApiAnalyticalEventsMockHandler(
  data?:
    | PostApiAnalyticalEventsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('/api/analytical/events', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiAnalyticalEventsMutationResponse(data)), {
      status: 202,
    });
  });
}
