/* Tailwind base */
@import "tailwindcss";
/* Tailwind theme */
@import "./colors-base.css";
@import "./colors-map.css";
@import "./colors-variables.css";
/* Tailwind v3 compatibility */
@import "./compatibility.css";

/* Import Tailwind plugins */
@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';

@custom-variant dark (&:is([data-mode="dark"] *));
@custom-variant radix-popper-wrapper (& [data-radix-popper-content-wrapper] );

@theme {
  --container-sm: 40rem;
  --container-md: 48rem;
  --container-lg: 64rem;
  --container-xl: 80rem;
  --container-2xl: 96rem;

  --font-sans: Inter var;

  --text-3xs: 0.375rem;
  --text-2xs: 0.5rem;

  --grid-template-columns-span-first: auto min-content;

  --spacing-50vh: 50vh;
  --spacing-80vh: 80vh;

  --z-index-navigation: 10;
  --z-index-popover: 20;
  --z-index-drawer: 30;
  --z-index-modal: 40;
  --z-index-select: 50;

  --translate-1\/5: 20%;

  --animate-slide-down-and-fade: slideDownAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1);
  --animate-slide-left-and-fade: slideLeftAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1);
  --animate-slide-up-and-fade: slideUpAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1);
  --animate-slide-right-and-fade: slideRightAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1);

  @keyframes slideDownAndFade {
    from {
      opacity: 0;
      transform: translateY(-2px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideLeftAndFade {
    from {
      opacity: 0;
      transform: translateX(2px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideUpAndFade {
    from {
      opacity: 0;
      transform: translateY(2px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideRightAndFade {
    from {
      opacity: 0;
      transform: translateX(-2px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
}
