import React, { type ComponentProps } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { TeamSchema } from '@shape-construction/api/src/types';
import Button from '@shape-construction/arch-ui/src/Button';
import { ArrowDownCircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import Link from '@shape-construction/arch-ui/src/Link';
import Spinner from '@shape-construction/arch-ui/src/Spinner';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { emailLinkFactory } from '@shape-construction/utils/Email';
import { environment } from 'app/config/environment';
import { useLocation } from 'react-router';
import type { SubscriptionPlan } from '../../../subscriptionPlans-config';

const WhiteArrowPath: React.FC<ComponentProps<'svg'>> = (props) => (
  <Spinner {...props} className={cn(props.className)} color="inverse" data-testid="subscribing-icon" />
);

type SubscriptionCardActionsProps = {
  canManageBilling: boolean;
  isActive: boolean;
  isSubscribing: boolean;
  isLoadingBillingLink: boolean;
  onSubscribe: (newPlanSlug: string) => void;
  onManageBilling: () => void;
  planSlug: SubscriptionPlan;
  teamId: TeamSchema['id'];
  teamName: TeamSchema['displayName'];
};

// Temporarily hiding Pro plan logic by commenting out the logic.
// When the Pro plan is ready to be shown on the app, please remove this comment and fix the unit tests:
// SubscriptionCardActions.spec.tsx, SubscriptionCard.spec.tsx, Subscription.spec.tsx

export const SubscriptionCardActions: React.FC<SubscriptionCardActionsProps> = ({
  canManageBilling,
  isActive,
  isSubscribing,
  isLoadingBillingLink,
  onSubscribe,
  onManageBilling,
  planSlug,
  teamName,
  teamId,
}) => {
  const subscriptionCardMessages = useMessageGetter('team.subscriptions.card.actions');
  const isFreePlan = planSlug === 'free';
  const location = useLocation();
  const subscriptionEmailInfoMapping: Record<string, { subject: string; body: string }> = {
    customDashboards: {
      subject: subscriptionCardMessages(`email.${planSlug}.subject`),
      body: subscriptionCardMessages(`email.${planSlug}.customDashboards.body`, { teamName, teamId }),
    },
    default: {
      subject: subscriptionCardMessages(`email.${planSlug}.subject`),
      body: subscriptionCardMessages(`email.${planSlug}.body`, { teamName, teamId }),
    },
  };

  const subscriptionEmailInfo =
    subscriptionEmailInfoMapping[location.state?.from] ?? subscriptionEmailInfoMapping.default;

  const emailUrl = emailLinkFactory({ email: environment.SUPPORT_EMAIL, ...subscriptionEmailInfo });

  if (isActive) {
    return (
      <div
        className={cn('text-gray-500 text-xs leading-4 font-normal py-2 text-center w-full flex justify-center', {
          'justify-between': canManageBilling,
        })}
        data-testid={`current-plan-${planSlug}`}
      >
        {subscriptionCardMessages('current')}
        {canManageBilling && (
          <Link
            as="button"
            color="primary"
            onClick={onManageBilling}
            className="cursor-pointer"
            aria-label={subscriptionCardMessages('manageBilling')}
          >
            {isLoadingBillingLink && <Spinner className="h-5 grow-0" data-testid="creating-billing-link-icon" />}
            {subscriptionCardMessages('manageBilling')}
          </Link>
        )}
      </div>
    );
  }

  return (
    <>
      {isFreePlan ? (
        <div className="flex justify-center w-full text-xs leading-4 font-medium h-8">
          <Link
            href={emailUrl}
            target="_blank"
            rel="noreferrer"
            color="primary"
            leadingIcon={ArrowDownCircleIcon}
            aria-label={subscriptionCardMessages('downgrade')}
          >
            {subscriptionCardMessages('downgrade')}
          </Link>
        </div>
      ) : (
        // <Button
        //   color="primary"
        //   variant="contained"
        //   size="xs"
        //   onClick={() => onSubscribe(planSlug)}
        //   leadingIcon={isSubscribing ? WhiteArrowPath : undefined}
        //   disabled={isSubscribing}
        //   aria-label={subscriptionCardMessages('subscribe')}
        // >
        //   {subscriptionCardMessages('subscribe')}
        // </Button>
        <a
          href={emailUrl}
          aria-label={subscriptionCardMessages('contactUs')}
          target="_blank"
          rel="noreferrer"
          className="w-full"
        >
          <Button color="primary" variant="contained" size="xs" fullWidth onClick={() => {}}>
            {subscriptionCardMessages('contactUs')}
          </Button>
        </a>
      )}
    </>
  );
};
