/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftReportsShiftReportIdRestoreMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftReportsShiftReportIdRestoreSchema';
import { createPostApiProjectsProjectIdShiftReportsShiftReportIdRestoreMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftReportsShiftReportIdRestore';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftReportsShiftReportIdRestoreMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdRestoreMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('/api/projects/:project_id/shift_reports/:shift_report_id/restore', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdShiftReportsShiftReportIdRestoreMutationResponse(data)),
      {
        status: 200,
      }
    );
  });
}
