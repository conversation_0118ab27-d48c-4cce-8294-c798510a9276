import React, { useRef, useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { IssueSchema } from '@shape-construction/api/src/types';
import { PlusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import * as Search from '@shape-construction/arch-ui/src/Search';
import { showErrorToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { useClickAway, useModal } from '@shape-construction/hooks';
import { keepPreviousData, useInfiniteQuery } from '@tanstack/react-query';
import { SearchLoading } from 'app/components/Search/SearchLoading';
import { SearchFieldLoadMoreButton } from 'app/components/SearchFieldLoadMoreButton/SearchFieldLoadMoreButton';
import { truncatedLocationPath } from 'app/components/Utils/locations';
import { useCurrentProject } from 'app/contexts/currentProject';
import { getProjectIssuesInfiniteQueryOptions } from 'app/queries/issues/issues';
import { useProjectLocations } from 'app/queries/projects/locations';
import { useCreateQuickIssue } from './useCreateQuickIssue';

const KEYBOARD_KEYS = {
  ENTER: 'Enter',
  ESCAPE: 'Escape',
} as const;

type CreateBlockerButtonProps = {
  searchTerm: string;
  selectBlocker: (issue: IssueSchema) => void;
};
const CreateBlockerButton: React.FC<CreateBlockerButtonProps> = ({ searchTerm, selectBlocker }) => {
  const messages = useMessageGetter('shiftManager.activities.readiness.blockers.search');
  const { onCreateQuickIssue } = useCreateQuickIssue();

  const handleClick = () => {
    if (!searchTerm) {
      showErrorToast({ message: messages('toasts.blankTitleError') });
      return;
    }

    onCreateQuickIssue(searchTerm, selectBlocker);
  };

  return (
    <Search.Button role="button" className="w-full truncate border-t border-neutral-subtlest" onClick={handleClick}>
      <PlusIcon className="w-3 h-3 mr-2" />
      {searchTerm
        ? messages('createButton.withBlockerName', { blockerName: searchTerm })
        : messages('createButton.emptyBlockerName')}
    </Search.Button>
  );
};

type BlockersSearchFieldProps = {
  onSelect: (issueId: IssueSchema['id']) => void;
  canEditActivityBlockers: boolean;
};
export const BlockersSearchField: React.FC<BlockersSearchFieldProps> = ({ onSelect, canEditActivityBlockers }) => {
  const messages = useMessageGetter('shiftManager.activities.readiness.blockers.search');
  const tooltipMessage = useMessageGetter('shiftManager.activities.readiness.blockers.tooltips');
  const project = useCurrentProject();
  const { data: locations = [] } = useProjectLocations(project.id);
  const { onCreateQuickIssue } = useCreateQuickIssue();

  const [searchTerm, setSearchTerm] = useState('');
  const isSearchMode = Boolean(searchTerm);
  const { open: optionsOpen, closeModal: closeOptions, openModal: openOptions } = useModal(false);

  const {
    data: searchResults,
    isLoading,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  } = useInfiniteQuery({
    ...getProjectIssuesInfiniteQueryOptions(project.id, { search: searchTerm || undefined }),
    enabled: Boolean(project.id),
    placeholderData: !isSearchMode ? keepPreviousData : undefined,
    select: (data) => data?.pages.flatMap(({ issues }) => issues.filter((issue) => issue.currentState !== 'resolved')),
  });

  const hasMoreToLoad = !isSearchMode && hasNextPage;
  const hasZeroResults = searchResults?.length === 0;

  const handleOnBlur = () => {
    setSearchTerm('');
    closeOptions();
  };

  const handleOnChange = (search: string) => {
    if (search.length > 1 || search.length === 0) {
      setSearchTerm(search);
      openOptions();
    }
  };

  const handleOnEnterOrEscape = (event: React.KeyboardEvent<HTMLElement>) => {
    if (event.key === KEYBOARD_KEYS.ENTER) {
      if (hasZeroResults && searchTerm) {
        onCreateQuickIssue(searchTerm, handleOnSelect);
      }
    } else if (event.key === KEYBOARD_KEYS.ESCAPE) {
      handleOnBlur();
    }
  };

  const handleOnSelect = (issue: IssueSchema) => {
    setSearchTerm('');
    onSelect(issue.id);
  };

  const renderResults = () => {
    if (isLoading) {
      return <SearchLoading />;
    }

    if (hasZeroResults) {
      return <CreateBlockerButton searchTerm={searchTerm} selectBlocker={handleOnSelect} />;
    }

    if (!searchResults) return null;

    return (
      <>
        <div className="overflow-auto max-h-80">
          {searchResults.map((issue) => (
            <Search.Option key={issue.id} value={issue}>
              <div className="w-full truncate">
                <span className="block truncate font-medium leading-5 text-neutral-bold">{issue.title}</span>
                <div className="flex gap-x-3 text-neutral-subtle text-xs">
                  {issue.referenceNumber && <span> {issue.referenceNumber}</span>}
                  {issue.locationId && <span>{truncatedLocationPath(locations, issue.locationId)}</span>}
                </div>
              </div>
            </Search.Option>
          ))}
          {hasMoreToLoad && (
            <SearchFieldLoadMoreButton
              isLoading={isFetchingNextPage}
              onClick={fetchNextPage}
              listName={messages('blockerSuffix')}
            />
          )}
        </div>
        <CreateBlockerButton searchTerm={searchTerm} selectBlocker={handleOnSelect} />
      </>
    );
  };

  const searchInputRef = useRef<HTMLDivElement>(null);
  useClickAway(searchInputRef, () => {
    if (optionsOpen) handleOnBlur();
  });

  return (
    <Tooltip.Root>
      <Tooltip.Trigger asChild>
        <div className="w-full" ref={searchInputRef}>
          <Search.Root onChange={handleOnSelect} value={searchTerm} disabled={!canEditActivityBlockers}>
            <Search.Field
              className="w-full truncate disabled:cursor-not-allowed disabled:opacity-50 hover:ring-0"
              placeholder={messages('placeholder')}
              onChange={handleOnChange}
              withSearchIcon
              variant="bordered"
              onFocus={openOptions}
              onKeyUp={handleOnEnterOrEscape}
            />
            {optionsOpen && (
              <div>
                <Search.Options static className="absolute z-popover mt-3 pt-2 pb-0 overflow-hidden max-h-max">
                  {renderResults()}
                </Search.Options>
              </div>
            )}
          </Search.Root>
        </div>
      </Tooltip.Trigger>
      <Tooltip.Content side="bottom" hidden={canEditActivityBlockers}>
        {tooltipMessage('noPermission.create')}
      </Tooltip.Content>
    </Tooltip.Root>
  );
};
