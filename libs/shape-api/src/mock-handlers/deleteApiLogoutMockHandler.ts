/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiLogoutMutationResponseSchema } from '../types/deleteApiLogoutSchema';
import { createDeleteApiLogoutMutationResponse } from '../factories/createDeleteApiLogout';
import { http } from 'msw';

export function deleteApiLogoutMockHandler(
  data?: DeleteApiLogoutMutationResponseSchema | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete('/api/logout', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createDeleteApiLogoutMutationResponse(data)), {
      status: 204,
    });
  });
}
