import { useMessageGetter } from '@messageformat/react';
import {
  postApiProjectsProjectIdIssuesIssueIdApprove,
  postApiProjectsProjectIdIssuesIssueIdComplete,
  postApiProjectsProjectIdIssuesIssueIdReject,
  postApiProjectsProjectIdIssuesIssueIdReopen,
  postApiProjectsProjectIdIssuesIssueIdStart,
  postApiProjectsProjectIdIssuesIssueIdStop,
} from '@shape-construction/api/src/api';
import {
  getApiProjectsProjectIdIssuesGroupCountQueryKey,
  getApiProjectsProjectIdIssuesIssueIdQueryKey,
  getApiProjectsProjectIdIssuesQueryKey,
} from '@shape-construction/api/src/hooks';
import type {
  IssueSchema,
  PostApiProjectsProjectIdIssuesIssueIdRejectMutationRequestSchema,
  ProjectSchema,
} from '@shape-construction/api/src/types';
import { showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { useMutation, useQueryClient } from '@tanstack/react-query';

type ActionType = 'approve' | 'complete' | 'reject' | 'reopen' | 'start' | 'stop';

const buildApproveMessage = (issue: IssueSchema, messages: ReturnType<typeof useMessageGetter>) => {
  const finalApprover = issue.approvers[issue.approvers.length - 1];
  const isLastApprover = issue.nextActionerId === finalApprover?.teamMemberId;

  if (!issue.nextActionerUser) return messages('approve.closed');
  if (isLastApprover) return messages('approve.final', { name: issue.nextActionerUser.name });

  return messages('approve.inProgress', { name: issue.nextActionerUser.name });
};

export const buildWorkflowMessage = (
  action: ActionType,
  issue: IssueSchema,
  messages: ReturnType<typeof useMessageGetter>
) => {
  switch (action) {
    case 'reject':
      return messages('reject');
    case 'approve':
      return buildApproveMessage(issue, messages);
    case 'start':
      return messages('start');
    case 'stop':
      return messages('stop');
    case 'complete':
      return messages('complete', { name: issue.approvers[0]?.user?.name ?? '' });
    case 'reopen':
      return messages('reopen');
    default:
      return '';
  }
};

export const useIssueAction = () => {
  const messages = useMessageGetter('issue.detail.actions');
  const queryClient = useQueryClient();

  const issueActions = {
    approve: postApiProjectsProjectIdIssuesIssueIdApprove,
    complete: postApiProjectsProjectIdIssuesIssueIdComplete,
    reject: postApiProjectsProjectIdIssuesIssueIdReject,
    reopen: postApiProjectsProjectIdIssuesIssueIdReopen,
    start: postApiProjectsProjectIdIssuesIssueIdStart,
    stop: postApiProjectsProjectIdIssuesIssueIdStop,
  };

  return useMutation({
    mutationFn: ({
      projectId,
      issueId,
      action,
      data = {},
    }: {
      projectId: ProjectSchema['id'];
      issueId: IssueSchema['id'];
      action: ActionType;
      data?: PostApiProjectsProjectIdIssuesIssueIdRejectMutationRequestSchema['issue'];
    }) => {
      if (action === 'reject') return issueActions.reject(projectId, issueId, { issue: data });

      return issueActions[action](projectId, issueId);
    },
    onSuccess: (updatedIssue, { projectId, issueId, action }) => {
      showSuccessToast({
        message: buildWorkflowMessage(action, updatedIssue, messages),
      });

      queryClient.invalidateQueries({
        queryKey: getApiProjectsProjectIdIssuesQueryKey(projectId),
      });
      queryClient.invalidateQueries({
        queryKey: getApiProjectsProjectIdIssuesGroupCountQueryKey(projectId),
        exact: false,
      });
      queryClient.setQueryData<IssueSchema>(
        getApiProjectsProjectIdIssuesIssueIdQueryKey(projectId, issueId),
        updatedIssue
      );
    },
  });
};
