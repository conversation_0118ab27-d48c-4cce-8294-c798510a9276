import React from 'react';
import type { TeamMemberSchema } from '@shape-construction/api/src/types';
import { UserAvatar } from '@shape-construction/arch-ui/src/Avatar';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import { TrashIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { PersonItem } from 'app/components/PersonItem/PersonItem';

interface ShiftReportCollaboratorItemProps {
  collaborator: TeamMemberSchema;
  handleDelete?: (name: TeamMemberSchema['id']) => void;
}

export const ShiftReportCollaboratorItem = ({ collaborator, handleDelete }: ShiftReportCollaboratorItemProps) => {
  const { id, user, team } = collaborator;

  return (
    <div className={cn('flex w-full gap-x-3 px-4 py-5')}>
      <div className="flex flex-1 justify-between">
        <PersonItem
          key={id}
          size="sm"
          avatar={
            <UserAvatar
              size="md"
              user={{
                id: user.id,
                name: user.name,
                firstName: user.firstName ?? undefined,
                lastName: user.lastName ?? undefined,
                avatarUrl: user.avatarUrl ?? undefined,
              }}
            />
          }
          primaryLabel={user.name}
          secondaryLabel={team.displayName || ''}
        />
        <div className="flex h-full gap-x-4">
          {handleDelete && (
            <IconButton
              color="secondary"
              variant="text"
              icon={TrashIcon}
              onClick={() => handleDelete(id)}
              size="md"
              aria-label="delete"
            />
          )}
        </div>
      </div>
    </div>
  );
};
