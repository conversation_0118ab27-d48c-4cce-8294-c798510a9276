import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema, WeeklyWorkPlanSchema } from '@shape-construction/api/src/types';
import Button from '@shape-construction/arch-ui/src/Button';
import { PlusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import Table from '@shape-construction/arch-ui/src/Table';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useWeeklyWorkPlanActivities } from 'app/queries/weeklyWorkPlans/weeklyWorkPlans';
import { DraggableActivities } from '../../../plan/components/Activities/DraggableActivities';
import { GroupedActivities } from '../../../plan/components/Activities/GroupedActivities';
import { useSearchFieldAutofocus } from '../../../plan/hooks/useSearchFieldAutofocus';
import { useWeeklyPlanFilters } from '../../../plan/hooks/useWeeklyPlanFilters';
import { lookbackColumns, useLookbackColumnOptions } from './componentTypes';
import { LookbackActivityRow } from './LookbackActivityRow';
import { PlaceholderRow } from './rows/PlaceholderRow';

export type LookbackPlanProps = {
  plan: WeeklyWorkPlanSchema;
  projectId: ProjectSchema['id'];
};

export const LookbackPlan: React.FC<LookbackPlanProps> = ({ plan, projectId }) => {
  const messages = useMessageGetter('weeklyPlanner.workPlans.lookback');
  const { data: planActivities, isLoading: isLoadingPlanActivities } = useWeeklyWorkPlanActivities(projectId, plan.id);
  const { lookbackColumnOptions } = useLookbackColumnOptions(plan);

  const {
    filters: { group },
  } = useWeeklyPlanFilters();
  const { focusSearchField } = useSearchFieldAutofocus();
  const hasGrouping = group !== 'none';
  const canEdit = plan.availableActions.edit;
  const isPublished = plan.status !== 'draft';
  const canAddAdditionalActivity = canEdit && isPublished;

  if (isLoadingPlanActivities || !planActivities?.entries) return null;

  return (
    <div className="overflow-x-auto h-full flex-1" data-testid="lookback-table">
      <Table className="table-fixed">
        <Table.Heading>
          <Table.Row className="bg-gray-200">
            {lookbackColumns.map((field) => {
              const Header = lookbackColumnOptions[field].header || Table.Header;
              return (
                <Header
                  key={field}
                  className={cn(
                    'pl-2 pr-2 py-1 sm:first:pl-2 sm:last:pr-2',
                    'border-r border-r-gray-300 last:border-r-0',
                    'normal-case text-gray-500 font-bold truncate'
                  )}
                >
                  {lookbackColumnOptions[field].title}
                </Header>
              );
            })}
          </Table.Row>
        </Table.Heading>

        <Table.Body className="bg-white text-gray-800">
          {hasGrouping ? (
            <GroupedActivities
              plan={plan}
              activities={planActivities?.entries}
              groupBy={group}
              projectId={projectId}
              lookback
            />
          ) : (
            <DraggableActivities
              projectId={projectId}
              plan={plan}
              items={planActivities?.entries}
              rowComponent={LookbackActivityRow}
            />
          )}
          {canAddAdditionalActivity && <PlaceholderRow plan={plan} />}
        </Table.Body>
        {canAddAdditionalActivity && (
          <tfoot>
            <Table.Row key="add-activity" className="bg-gray-50 min-h-11">
              <Table.Cell colSpan={100} className="p-1 sm:first:pl-1 sm:last:pr-0">
                <Tooltip.Root>
                  <Tooltip.Trigger asChild>
                    <Button
                      aria-label={messages('addAdditionalActivityCTA')}
                      leadingIcon={PlusIcon}
                      color="primary"
                      size="md"
                      variant="text"
                      onClick={focusSearchField}
                    >
                      {messages('addAdditionalActivityCTA')}
                    </Button>
                  </Tooltip.Trigger>
                  <Tooltip.Content side="bottom">{messages('additionalActivityCTAPopup')}</Tooltip.Content>
                </Tooltip.Root>
              </Table.Cell>
            </Table.Row>
          </tfoot>
        )}
      </Table>
    </div>
  );
};
