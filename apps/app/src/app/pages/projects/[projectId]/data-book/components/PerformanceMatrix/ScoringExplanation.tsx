import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Divider from '@shape-construction/arch-ui/src/Divider';
import { ScoringExplanationListHeader } from './ScoringExplanationListHeader';
import { ScoringExplanationListItem } from './ScoringExplanationListItem';

export const ScoringExplanation: React.FC = () => {
  const messages = useMessageGetter(
    'dataBook.page.heatmapDashboard.performanceDetails.issueReportsTable.scoringExplanation'
  );
  return (
    <>
      <div className="p-2">
        <div className="flex justify-between items-center">
          <div className="text-sm leading-5 font-medium text-gray-700">{messages('title')}</div>
        </div>
      </div>

      <Divider orientation="horizontal" />

      <ScoringExplanationListHeader title={messages('basics.title')} />
      <ScoringExplanationListItem text={messages('basics.item1')} value="10%" />
      <ScoringExplanationListItem text={messages('basics.item2')} value="10%" />
      <ScoringExplanationListItem text={messages('basics.item3')} value="10%" />

      <Divider orientation="horizontal" />

      <ScoringExplanationListHeader title={messages('additionalDetails.title')} />
      <ScoringExplanationListItem text={messages('additionalDetails.item1')} value="10%" />
      <ScoringExplanationListItem text={messages('additionalDetails.item2')} value="10%" />
      <ScoringExplanationListItem text={messages('additionalDetails.item3')} value="10%" />
      <ScoringExplanationListItem text={messages('additionalDetails.item4')} value="5%" />
      <ScoringExplanationListItem text={messages('additionalDetails.item5')} value="5%" />
      <ScoringExplanationListItem text={messages('additionalDetails.item6')} value="20%" />
      <ScoringExplanationListItem text={messages('additionalDetails.item7')} value="10%" />

      <Divider orientation="horizontal" />

      <ScoringExplanationListHeader title={messages('notes.title')} />
      <ScoringExplanationListItem text={messages('notes.item1')} />
      <ScoringExplanationListItem text={messages('notes.item2')} />
    </>
  );
};
