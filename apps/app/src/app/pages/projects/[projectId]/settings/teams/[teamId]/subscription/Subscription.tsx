import React, { useEffect, useState } from 'react';
import { useMessage } from '@messageformat/react';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import {
  useManageSubscriptionBilling,
  useSubscribeTeam,
} from 'app/queries/teamsSubscriptionPlan/teamsSubscriptionPlan';
import { useNavigate } from 'react-router';
import { useTeamPage } from '../hooks/useTeamPage';
import { CheckoutErrorModal } from './components/CheckoutErrorModal/CheckoutErrorModal';
import { SubscriptionCard } from './components/SubscriptionCard/SubscriptionCard';
import { subscriptionPlans } from './subscriptionPlans-config';

export const Subscription = () => {
  const navigate = useNavigate();
  const [openCheckoutErrorModal, setCheckoutErrorModal] = useState<boolean>(false);
  const {
    canEditTeamSubscription,
    canManageBilling,
    currentSubscribedPlan,
    isLoadingTeamSubscription,
    projectId,
    refetchTeamSubscription,
    teamId,
    team,
  } = useTeamPage();

  const { mutate: createTeamSubscription, isPending: isLoadingSubscriptionLink } = useSubscribeTeam();

  const untitledTeamName = useMessage('team.untitled');
  const teamName = team?.displayName || untitledTeamName;

  const onSubscribe = (newPlanSlug: string) => {
    createTeamSubscription(
      {
        projectId,
        teamId,
        data: { new_plan_slug: newPlanSlug },
      },
      {
        onSuccess: ({ paymentRequired, paymentRedirectUrl }) => {
          if (paymentRequired) {
            window.location.href = paymentRedirectUrl as string;
          } else {
            refetchTeamSubscription();
          }
        },
        onError: () => setCheckoutErrorModal(true),
      }
    );
  };

  const { isPending: isLoadingBillingLink, mutate: createBillingPortalLink } = useManageSubscriptionBilling();

  const onManageBilling = () => {
    createBillingPortalLink(
      {
        projectId,
        teamId,
      },
      {
        onSuccess: ({ portalUrl }) => {
          if (portalUrl) {
            window.location.href = portalUrl as string;
          }
        },
      }
    );
  };

  useEffect(() => {
    if (isLoadingTeamSubscription) return;

    if (!canEditTeamSubscription) {
      navigate(`/projects/${projectId}/settings/teams`);
    }
  }, [canEditTeamSubscription, navigate, projectId, isLoadingTeamSubscription]);

  if (isLoadingTeamSubscription || !canEditTeamSubscription) return null;

  return (
    <>
      <div className="relative flex flex-1 flex-col bg-gray-100 md:min-h-0 md:overflow-hidden md:overflow-y-auto px-4 py-4 sm:px-8 sm:py-6">
        <div className="@container">
          <div className="grid grid-cols-4 gap-4 @sm:grid-cols-12 @sm:gap-6">
            {subscriptionPlans.map((planSlug, index) => (
              <div
                key={`card-${planSlug}`}
                className={cn('col-span-4 @sm:col-span-6 @lg:col-span-3', {
                  '@5xl:col-start-4': index === 0,
                })}
              >
                <SubscriptionCard
                  isActive={currentSubscribedPlan === planSlug}
                  onSubscribe={onSubscribe}
                  onManageBilling={onManageBilling}
                  planSlug={planSlug}
                  isSubscribing={isLoadingSubscriptionLink}
                  isLoadingBillingLink={isLoadingBillingLink}
                  teamId={teamId}
                  teamName={teamName}
                  canManageBilling={canManageBilling}
                />
              </div>
            ))}
          </div>
        </div>
      </div>
      <CheckoutErrorModal open={openCheckoutErrorModal} onClose={() => setCheckoutErrorModal(false)} />
    </>
  );
};
