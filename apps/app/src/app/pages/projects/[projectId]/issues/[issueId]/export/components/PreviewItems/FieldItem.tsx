import React, { memo } from 'react';
import { cn } from '@shape-construction/arch-ui/src/utils/classes.ts';
import { fieldTypes } from 'app/constants/FieldTypes';
import { usePrintItemToggle } from '../../hooks/usePrintItemToggle';
import type { FormField, IssueItemPeopleTeams } from '../../types.ts';
import { PersonTeamField } from './PersonTeamField';
import { SwitchableRow } from './SwitchableRow';

type FieldTypeProps = {
  field: FormField;
  fieldType: keyof typeof fieldTypes;
};

const FieldType: React.FC<FieldTypeProps> = ({ fieldType, field }) => {
  switch (fieldType) {
    case fieldTypes.normal:
      return <p className="align-text-top text-gray-900">{field.value}</p>;
    case fieldTypes.bold:
      return <p className="align-text-top text-sm font-medium leading-5 text-gray-900">{field.value}</p>;
    case fieldTypes.personTeam:
      return <PersonTeamField field={field as IssueItemPeopleTeams} />;
    default:
      return null;
  }
};

export interface FieldProps {
  field: FormField;
}

export const FieldItem: React.FC<FieldProps> = memo(({ field }) => {
  const { handleToggle } = usePrintItemToggle(field);
  const fieldType = field.fieldType || fieldTypes.bold;
  const visibilityKey = `${field.key}.visible`;

  // custom fields require a wider spacing
  const spacing = field?.itemType === 'field' && field?.key.startsWith('customFields') ? 'w-56' : 'w-36';

  return (
    <SwitchableRow className="gap-x-0 md:gap-x-4 md:pl-5 items-start mb-4" field={field} onToggle={handleToggle}>
      <div
        className="flex flex-col items-start justify-between gap-x-2 md:flex-row md:justify-start"
        data-testid={visibilityKey}
      >
        <span className={cn(spacing, 'shrink-0 text-sm font-medium leading-5 text-gray-500')}>{field.title}</span>
        <FieldType fieldType={fieldType} field={field} />{' '}
      </div>
    </SwitchableRow>
  );
});
