/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiUsersPasswordInstructionsMutationResponseSchema } from '../types/postApiUsersPasswordInstructionsSchema';
import { createPostApiUsersPasswordInstructionsMutationResponse } from '../factories/createPostApiUsersPasswordInstructions';
import { http } from 'msw';

export function postApiUsersPasswordInstructionsMockHandler(
  data?:
    | PostApiUsersPasswordInstructionsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('/api/users/password/instructions', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiUsersPasswordInstructionsMutationResponse(data)), {
      status: 204,
    });
  });
}
