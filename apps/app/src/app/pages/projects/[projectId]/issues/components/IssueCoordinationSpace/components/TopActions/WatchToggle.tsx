import React, { type ComponentProps } from 'react';
import { useMessage } from '@messageformat/react';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import { EyeIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useIssueWatch } from 'app/queries/issues/issues';
import { useSelectedIssue } from '../../hooks/issueContext';

const ToggleButton = (props: Omit<ComponentProps<typeof IconButton>, 'color' | 'variant' | 'size'>) => (
  <IconButton color="secondary" variant="text" size="md" {...props} />
);

const BlueEyeSlashIcon: React.FC<ComponentProps<typeof EyeIcon>> = ({ className, ...props }) => (
  <EyeIcon {...props} className={cn(className, 'text-indigo-500')} />
);

const WatchToggle = () => {
  const [issue] = useSelectedIssue();
  const buttonTitle = useMessage('issue.detail.topActions.watch', {
    ISWATCHING: issue?.isWatching,
  });
  const { mutate: toggleWatch, isPending } = useIssueWatch(issue?.projectId as string, issue?.id as string);
  const isWatching = issue?.isWatching;

  const CheckedButton = (
    <ToggleButton
      aria-label="checked"
      disabled={isPending}
      icon={BlueEyeSlashIcon}
      title={buttonTitle}
      onClick={() => toggleWatch(!isWatching)}
    />
  );

  const UnCheckedButton = (
    <ToggleButton
      aria-label="unchecked"
      disabled={isPending}
      icon={EyeIcon}
      title={buttonTitle}
      onClick={() => toggleWatch(!isWatching)}
    />
  );

  if (!issue) return null;
  if (issue.currentState === 'draft') return null;

  return isWatching ? CheckedButton : UnCheckedButton;
};

export default WatchToggle;
