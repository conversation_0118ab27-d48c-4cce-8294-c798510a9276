// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type {
  PostApiProjectsProjectIdIssuesIssueIdRejectPathParamsSchema,
  PostApiProjectsProjectIdIssuesIssueIdRejectMutationRequestSchema,
  PostApiProjectsProjectIdIssuesIssueIdRejectMutationResponseSchema,
} from '../types/postApiProjectsProjectIdIssuesIssueIdRejectSchema';
import { createAuthenticationError } from './createAuthenticationError';
import { createError } from './createError';
import { createIssue } from './createIssue';
import { faker } from '@faker-js/faker';

export function createPostApiProjectsProjectIdIssuesIssueIdRejectPathParams(
  data?: Partial<PostApiProjectsProjectIdIssuesIssueIdRejectPathParamsSchema>
): PostApiProjectsProjectIdIssuesIssueIdRejectPathParamsSchema {
  faker.seed([100]);
  return {
    ...{ project_id: faker.string.uuid(), issue_id: faker.string.uuid() },
    ...(data || {}),
  };
}

/**
 * @description Issue updated
 */
export function createPostApiProjectsProjectIdIssuesIssueIdReject200() {
  faker.seed([100]);
  return createIssue();
}

/**
 * @description Bad request
 */
export function createPostApiProjectsProjectIdIssuesIssueIdReject400() {
  faker.seed([100]);
  return createError();
}

/**
 * @description Authentication required
 */
export function createPostApiProjectsProjectIdIssuesIssueIdReject401() {
  faker.seed([100]);
  return createAuthenticationError();
}

/**
 * @description Issue not found
 */
export function createPostApiProjectsProjectIdIssuesIssueIdReject404() {
  faker.seed([100]);
  return undefined;
}

export function createPostApiProjectsProjectIdIssuesIssueIdRejectMutationRequest(
  data?: Partial<PostApiProjectsProjectIdIssuesIssueIdRejectMutationRequestSchema>
): PostApiProjectsProjectIdIssuesIssueIdRejectMutationRequestSchema {
  faker.seed([100]);
  return {
    ...{
      issue: {
        reject_reason: faker.string.alpha(),
        user_reject_resolve_status: faker.helpers.arrayElement<
          NonNullable<
            NonNullable<PostApiProjectsProjectIdIssuesIssueIdRejectMutationRequestSchema>['issue']
          >['user_reject_resolve_status']
        >(['assigned', 'in_progress']),
      },
    },
    ...(data || {}),
  };
}

export function createPostApiProjectsProjectIdIssuesIssueIdRejectMutationResponse(
  data?: Partial<PostApiProjectsProjectIdIssuesIssueIdRejectMutationResponseSchema>
): PostApiProjectsProjectIdIssuesIssueIdRejectMutationResponseSchema {
  faker.seed([100]);
  return data || faker.helpers.arrayElement<any>([createPostApiProjectsProjectIdIssuesIssueIdReject200()]);
}
