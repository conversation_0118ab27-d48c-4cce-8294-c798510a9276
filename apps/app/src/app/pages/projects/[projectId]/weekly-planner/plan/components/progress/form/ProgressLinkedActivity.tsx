import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { WeeklyWorkPlanActivitySchema } from '@shape-construction/api/src/types';
import Button from '@shape-construction/arch-ui/src/Button';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import IssueCardUniquRef from 'app/components/IssueCard/components/IssueCardUniqueRef/IssueCardUniquRef';
import ActivityCardLocation from './ActivityCardLocation';
import ActivityPercentageCard from './ActivityPercentageCard';

interface ProgressLinkedActivityProp {
  activity: WeeklyWorkPlanActivitySchema;
  unlink?: () => void;
  readOnly?: boolean;
}

export const ProgressLinkedActivity: React.FC<ProgressLinkedActivityProp> = ({ activity, unlink, readOnly }) => {
  const messages = useMessageGetter('weeklyPlanner.workPlans.progressTracker.drawer');
  const { shiftActivity } = activity;

  if (!shiftActivity) return null;

  return (
    <Tooltip.Root>
      <Tooltip.Trigger asChild>
        <div
          className={cn(
            'flex flex-col w-full truncate p-3 gap-x-3 gap-y-2 bg-white rounded-md border border-gray-300 items-center',
            {
              'cursor-not-allowed': readOnly,
            }
          )}
        >
          <div className="w-full flex items-center truncate">
            <div className="w-full truncate">
              <span className="truncate">{shiftActivity.description}</span>
            </div>
            {!readOnly && (
              <Button color="primary" size="xxs" variant="text" onClick={unlink}>
                {messages('unlinkActivityCTA')}
              </Button>
            )}
          </div>

          <div className="flex flex-row flex-wrap items-center gap-x-4 gap-y-2 w-full truncate">
            <IssueCardUniquRef uniqueRef={shiftActivity.referenceNumber} />
            <ActivityCardLocation locationId={shiftActivity.locationId} />
            <ActivityPercentageCard percentage={shiftActivity.percentageCompleted} />
          </div>
        </div>
      </Tooltip.Trigger>
      <Tooltip.Content side="bottom" hidden={!readOnly}>
        {messages('activityReadOnlyTooltip')}
      </Tooltip.Content>
    </Tooltip.Root>
  );
};
