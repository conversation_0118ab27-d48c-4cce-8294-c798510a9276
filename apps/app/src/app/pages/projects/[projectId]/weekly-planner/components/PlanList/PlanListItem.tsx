import React, { useMemo, useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { WeeklyWorkPlanSchema } from '@shape-construction/api/src/types';
import { UserAvatar } from '@shape-construction/arch-ui/src/Avatar';
import Badge from '@shape-construction/arch-ui/src/Badge';
import { THEME } from '@shape-construction/arch-ui/src/Badge/Badge.types';
import Table from '@shape-construction/arch-ui/src/Table';
import { showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useFeatureFlag } from '@shape-construction/feature-flags';
import { useStableCallback } from '@shape-construction/hooks';
import { formatDateAndTime, parseDate, parseDateWithFormat } from '@shape-construction/utils/DateTime';
import { TIMEZONE_DEFAULT } from 'app/constants/DateTime';
import { useCurrentProject } from 'app/contexts/currentProject';
import { PlanListItemPublishLookbackButton } from 'app/pages/projects/[projectId]/weekly-planner/components/PlanList/PlanListItemPublishLookbackButton';
import { PlanListItemPublishPlanButton } from 'app/pages/projects/[projectId]/weekly-planner/components/PlanList/PlanListItemPublishPlanButton';
import { useProjectPerson } from 'app/queries/projects/people';
import { useCurrentUser } from 'app/queries/users/users';
import { useArchiveWeeklyWorkPlan, useRestoreWeeklyWorkPlan } from 'app/queries/weeklyWorkPlans/weeklyWorkPlans';
import { useNavigate, useParams } from 'react-router';
import type { TabHref } from '../../constants';
import { PLAN_STATUS, PLAN_STATUS_COLORS, PLAN_STATUS_LABELS, type PlanStatus } from '../../plan/constants/constants';
import { DuplicatePlan } from '../DuplicatePlan/DuplicatePlan';
import { EditPlan } from '../EditPlan/EditPlan';
import { PlanListItemDropdown } from './PlanListItemDropdown';

type Params = { tabId: TabHref };

export type PlanListItemProps = { plan: WeeklyWorkPlanSchema };

const Status = ({ status }: { status: WeeklyWorkPlanSchema['status'] }) => {
  if (status === PLAN_STATUS.QUEUED) return null;

  return <Badge theme={(PLAN_STATUS_COLORS[status] as THEME) || THEME.GRAY} label={PLAN_STATUS_LABELS[status]} />;
};

export const PlanListItem: React.FC<PlanListItemProps> = ({ plan }) => {
  const { value: isStreamliningProgressLogs } = useFeatureFlag('streamlining-progress-logs');
  const messages = useMessageGetter('weeklyPlanner.workPlans.table');
  const { tabId } = useParams() as Params;
  const project = useCurrentProject();
  const { data: author } = useProjectPerson(project.id, plan.teamMemberId);
  const [openPlanModal, setOpenPlanModal] = useState(false);
  const [openDuplicatePlanModal, setOpenDuplicatePlanModal] = useState(false);
  const { mutate: archiveWeeklyWorkPlan } = useArchiveWeeklyWorkPlan();
  const { mutate: restoreWeeklyWorkPlan } = useRestoreWeeklyWorkPlan();

  const currentUser = useCurrentUser();

  const timezone = project?.timezone || currentUser.timezone || TIMEZONE_DEFAULT;

  const [startDate, endDate] = useMemo(() => {
    const start = parseDate(plan.startDate);
    const end = parseDate(plan.endDate);
    const startFormat = start.year() !== end.year() ? 'DD-MMM-YYYY' : 'DD-MMM';

    return [parseDateWithFormat(start, startFormat), parseDateWithFormat(end, 'DD-MMM-YYYY')];
  }, [plan.endDate, plan.startDate]);

  const navigate = useNavigate();

  const selectPlanTab = (planStatus: PlanStatus): string => {
    const basePath = `/projects/${project.id}/weekly-planner/plans/${plan.id}`;
    switch (planStatus) {
      case PLAN_STATUS.TRACKING:
        return isStreamliningProgressLogs ? `${basePath}/lookback` : `${basePath}/progress-logs`;
      case PLAN_STATUS.REVIEWING:
        return `${basePath}/lookback`;
      case PLAN_STATUS.CLOSED:
        return `${basePath}/lookback`;
      default:
        return basePath;
    }
  };

  const handleRowClick = useStableCallback(() => {
    navigate(selectPlanTab(plan.status), { state: { tab: tabId } });
  });

  const isYou = currentUser?.id === author?.user.id;

  const rowBackgroundColorClassName = cn(
    'bg-white',
    { 'bg-pink-50': plan.status === PLAN_STATUS.REVIEWING },
    { 'bg-green-50': plan.status === PLAN_STATUS.TRACKING }
  );

  const onArchive = () => {
    archiveWeeklyWorkPlan(
      {
        projectId: project.id,
        weeklyWorkPlanId: plan.id,
      },
      {
        onSuccess: () => {
          showSuccessToast({ message: messages('archivePlan.successToast', { planName: plan.title }) });
        },
      }
    );
  };

  const onRestore = () => {
    restoreWeeklyWorkPlan(
      {
        projectId: project.id,
        weeklyWorkPlanId: plan.id,
      },
      {
        onSuccess: () => {
          showSuccessToast({
            message: messages('restorePlan.successToast', {
              planName: plan.title,
            }),
          });
        },
      }
    );
  };

  return (
    <>
      <Table.Row key={plan.id} className={rowBackgroundColorClassName} onClick={handleRowClick}>
        <Table.Cell className="text-gray-800 max-w-36">
          <Tooltip.Root>
            <Tooltip.Trigger asChild>
              <div className="truncate">{plan.title}</div>
            </Tooltip.Trigger>
            <Tooltip.Content side="top">{plan.title}</Tooltip.Content>
          </Tooltip.Root>
        </Table.Cell>

        <Table.Cell className="text-gray-800">
          {startDate} – {endDate}
        </Table.Cell>
        <Table.Cell>
          <div className="flex items-center gap-x-2">
            <UserAvatar highlighted highlightedColor="primary" user={author?.user} size="sm" />
            <div>
              <span className="font-medium text-gray-900">{author?.user.name}</span>
              {isYou && <span className="text-gray-500"> {messages('you')}</span>}
            </div>
          </div>
        </Table.Cell>
        <Table.Cell className="text-gray-800">
          {formatDateAndTime(plan.lastActivityAt, timezone, 'DD-MMM-YYYY HH:mm')}
        </Table.Cell>
        <Table.Cell>
          <Status status={plan.status} />
        </Table.Cell>
        <Table.Cell className="flex justify-end" onClick={(e) => e.stopPropagation()}>
          {plan.availableActions?.close && (
            <span className="mr-2">
              <PlanListItemPublishLookbackButton plan={plan} projectId={project.id} />
            </span>
          )}
          {plan.availableActions?.publish && (
            <span className="mr-2">
              <PlanListItemPublishPlanButton plan={plan} projectId={project.id} />
            </span>
          )}
          {author && (
            <PlanListItemDropdown
              plan={plan}
              onEdit={() => setOpenPlanModal(true)}
              onDuplicate={() => setOpenDuplicatePlanModal(true)}
              onArchive={onArchive}
              onRestore={onRestore}
            />
          )}
        </Table.Cell>
      </Table.Row>
      <EditPlan
        initialValues={{
          title: plan.title,
          start_date: plan.startDate,
          end_date: plan.endDate,
        }}
        planId={plan.id}
        open={openPlanModal}
        onClose={() => setOpenPlanModal(false)}
      />
      <DuplicatePlan
        initialValues={{
          title: `Copy of ${plan.title}`,
          start_date: plan.endDate,
        }}
        planId={plan.id}
        open={openDuplicatePlanModal}
        onClose={() => setOpenDuplicatePlanModal(false)}
      />
    </>
  );
};
