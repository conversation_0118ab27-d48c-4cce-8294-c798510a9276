import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ShiftActivitySchema } from '@shape-construction/api/src/types';
import Badge from '@shape-construction/arch-ui/src/Badge';
import { THEME } from '@shape-construction/arch-ui/src/Badge/Badge.types';
import Button from '@shape-construction/arch-ui/src/Button';
import Drawer from '@shape-construction/arch-ui/src/Drawer';
import { InformationCircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import {
  ArrowsPointingOutIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  IdentificationIcon,
  MapPinIcon,
  PencilSquareIcon,
  UserIcon,
  UsersIcon,
} from '@shape-construction/arch-ui/src/Icons/solid';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useModal } from '@shape-construction/hooks';
import { useQuery } from '@tanstack/react-query';
import { ActivityStatusBadge } from 'app/components/ShiftManager/Activities/components/ActivityStatusBadge/ActivityStatusBadge';
import { fullLocationPath, truncatedLocationPath } from 'app/components/Utils/locations';
import { useCurrentProject } from 'app/contexts/currentProject';
import { useProjectLocations } from 'app/queries/projects/locations';
import { getProjectPersonQueryOptions } from 'app/queries/projects/people';
import { EditActivity } from 'app/screens/Project/ProjectShiftManager/Activities/EditActivity/EditActivity';
import { useNavigate } from 'react-router';
import { ActivityTimeTable } from './components/ActivityTimeTable';
import { IconText } from './components/IconText';
import { Widget } from './components/Widget';

type ActivityDetailsWidgetProps = {
  shiftActivity?: ShiftActivitySchema;
  timezone?: string;
  expanded?: boolean;
  isLoading?: boolean;
  isError?: boolean;
};

export const ActivityDetailsWidget: React.FC<ActivityDetailsWidgetProps> = ({
  shiftActivity,
  isLoading,
  timezone,
  expanded,
  isError,
}) => {
  const messages = useMessageGetter('shiftManager.activities.details');
  const project = useCurrentProject();
  const navigate = useNavigate();
  const {
    open: isEditActivityModalOpen,
    openModal: openEditActivityModal,
    closeModal: closeEditActivityModal,
  } = useModal(false);

  const { data: locations = [] } = useProjectLocations(project.id);
  const { data: owner } = useQuery(getProjectPersonQueryOptions(project.id, shiftActivity?.ownerId!));

  if (!shiftActivity || !timezone) return <Widget isLoading={isLoading} isError={isError} title={messages('title')} />;

  const {
    actualEndDate,
    actualStartDate,
    availableActions,
    critical,
    expectedFinishDate,
    locationId,
    organisation,
    percentageCompleted,
    plannedEndDate,
    plannedStartDate,
    referenceNumber,
    status,
    taskIdentifier,
  } = shiftActivity;

  const ownerName = owner?.user.name;
  const locationPath = truncatedLocationPath(locations, locationId);
  const fullPath = locationId ? fullLocationPath(locations, locationId) : null;

  return (
    <>
      <Widget
        isLoading={isLoading}
        isError={isError}
        title={messages('title')}
        actions={
          <>
            {availableActions?.edit && (
              <Button
                aria-label={messages('actions.edit')}
                size="xxs"
                color="secondary"
                variant="outlined"
                leadingIcon={PencilSquareIcon}
                onClick={() => {
                  openEditActivityModal();
                }}
              >
                {messages('actions.edit')}
              </Button>
            )}
            {!expanded && (
              <Button
                aria-label={messages('actions.seeAll')}
                size="xxs"
                color="secondary"
                variant="outlined"
                leadingIcon={ArrowsPointingOutIcon}
                onClick={() => {
                  navigate(`/projects/${project.id}/activities/${shiftActivity.id}/details`);
                }}
              >
                {messages('actions.seeAll')}
              </Button>
            )}
          </>
        }
      >
        <div className="flex flex-row flex-wrap gap-2">
          <IconText
            icon={<IdentificationIcon width={18} height={18} />}
            label={messages('shapeId')}
            data={
              <Tooltip.Root>
                <Tooltip.Trigger className="flex flex-row align-center gap-2">
                  {referenceNumber || '-'}
                  <InformationCircleIcon aria-label="tool" width={18} height={18} className="text-icon-selected-bold" />
                </Tooltip.Trigger>
                <Tooltip.Content side="bottom">{messages('tooltip.shapeId')}</Tooltip.Content>
              </Tooltip.Root>
            }
          />
          <IconText
            icon={<IdentificationIcon width={18} height={18} />}
            label={messages('taskId')}
            data={
              <Tooltip.Root>
                <Tooltip.Trigger className="flex flex-row align-center gap-2">
                  {taskIdentifier || '-'}
                  <InformationCircleIcon width={18} height={18} className="text-icon-selected-bold" />
                </Tooltip.Trigger>
                <Tooltip.Content side="bottom">{messages('tooltip.taskId')}</Tooltip.Content>
              </Tooltip.Root>
            }
          />
          <IconText
            icon={<MapPinIcon width={18} height={18} />}
            label={messages('location')}
            data={
              locationPath ? (
                <Tooltip.Root>
                  <Tooltip.Trigger className="flex flex-row align-center gap-2">{locationPath}</Tooltip.Trigger>
                  <Tooltip.Content side="bottom">{fullPath}</Tooltip.Content>
                </Tooltip.Root>
              ) : (
                '-'
              )
            }
          />
          <IconText
            icon={<UsersIcon width={18} height={18} />}
            label={messages('organisation')}
            data={organisation || '-'}
          />
          <IconText
            icon={<ClockIcon className="text-icon-selected-bold" width={18} height={18} />}
            label={messages('status')}
            data={<ActivityStatusBadge status={status as ShiftActivitySchema['status']} />}
          />
          <IconText
            icon={
              <ExclamationTriangleIcon className={cn({ 'text-icon-danger-bold': critical })} width={18} height={18} />
            }
            label={messages('critical.title')}
            data={
              <Badge
                theme={critical ? THEME.RED : THEME.GRAY}
                label={critical ? messages('critical.yes') : messages('critical.no')}
              />
            }
          />
          <IconText
            icon={<CheckCircleIcon width={18} height={18} />}
            label={messages('progress')}
            data={percentageCompleted ? `${percentageCompleted}%` : '-'}
          />
          <IconText icon={<UserIcon width={18} height={18} />} label={messages('owner')} data={ownerName || '-'} />
        </div>
        <ActivityTimeTable
          timezone={timezone}
          plannedStartDate={plannedStartDate}
          plannedEndDate={plannedEndDate}
          actualStartDate={actualStartDate}
          actualEndDate={actualEndDate}
          expectedFinishDate={expectedFinishDate}
        />
      </Widget>

      <Drawer.Root
        open={isEditActivityModalOpen}
        onClose={closeEditActivityModal}
        maxWidth="lg"
        zIndex={10}
        data-testid={'edit-activity-form'}
        data-cy={'edit-activity-form'}
      >
        <EditActivity
          projectId={project.id}
          shiftActivityId={shiftActivity.id}
          onClose={closeEditActivityModal}
          timezone={timezone}
        />
      </Drawer.Root>
    </>
  );
};
