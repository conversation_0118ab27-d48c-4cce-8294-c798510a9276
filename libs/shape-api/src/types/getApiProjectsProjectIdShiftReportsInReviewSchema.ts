/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ShiftReportListSchema } from './shiftReportListSchema';

export type GetApiProjectsProjectIdShiftReportsInReviewPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export type GetApiProjectsProjectIdShiftReportsInReviewQueryParamsSchema = {
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
};

/**
 * @description List of in_review shift reports
 */
export type GetApiProjectsProjectIdShiftReportsInReview200Schema = ShiftReportListSchema;

/**
 * @description Bad request
 */
export type GetApiProjectsProjectIdShiftReportsInReview400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftReportsInReview401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdShiftReportsInReview403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftReportsInReview404Schema = void;

export type GetApiProjectsProjectIdShiftReportsInReviewQueryResponseSchema =
  GetApiProjectsProjectIdShiftReportsInReview200Schema;

export type GetApiProjectsProjectIdShiftReportsInReviewSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftReportsInReview200Schema;
  PathParams: GetApiProjectsProjectIdShiftReportsInReviewPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdShiftReportsInReviewQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftReportsInReview400Schema
    | GetApiProjectsProjectIdShiftReportsInReview401Schema
    | GetApiProjectsProjectIdShiftReportsInReview403Schema
    | GetApiProjectsProjectIdShiftReportsInReview404Schema;
};
