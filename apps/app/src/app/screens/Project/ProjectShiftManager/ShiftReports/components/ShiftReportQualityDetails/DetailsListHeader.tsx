import React from 'react';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';

export type DetailsListHeaderProps = {
  title: string;
  progressPercentagesDescription?: string; // Example: "10/20%"
  isComplete?: boolean;
  isDeactivated?: boolean;
};

export const DetailsListHeader: React.FC<DetailsListHeaderProps> = ({
  title,
  progressPercentagesDescription,
  isComplete,
  isDeactivated,
}) => {
  return (
    <div className="flex pt-3 pb-1 justify-between items-center">
      <div className="text-xs leading-4 font-semibold tracking-wider uppercase text-gray-400">{title}</div>
      {progressPercentagesDescription && (
        <div
          className={cn('text-xs leading-4 font-medium', {
            'opacity-50': isDeactivated,
            'text-gray-400': !isComplete,
            'text-green-500': isComplete && !isDeactivated,
            'text-gray-700': isComplete && isDeactivated,
          })}
        >
          {progressPercentagesDescription}
        </div>
      )}
    </div>
  );
};
