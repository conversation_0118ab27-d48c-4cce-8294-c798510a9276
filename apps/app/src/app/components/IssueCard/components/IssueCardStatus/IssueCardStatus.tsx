import React from 'react';
import type { IssueSchema } from '@shape-construction/api/src/types';
import Badge from '@shape-construction/arch-ui/src/Badge';
import { THEME } from '@shape-construction/arch-ui/src/Badge/Badge.types';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { ISSUE_STATE_COLORS } from '../../../../constants/IssueStates';

export type IssueCardStatusProps = {
  className?: string;
  text: string;
  status: IssueSchema['currentState'];
  truncateBadge?: boolean;
};

const IssueCardStatus = ({ className, text, status, truncateBadge = false }: IssueCardStatusProps) => (
  <div className={cn('whitespace-nowrap', className)}>
    <Badge theme={(ISSUE_STATE_COLORS[status] as THEME) || THEME.GRAY} label={text} truncate={truncateBadge} />
  </div>
);

export default React.memo(IssueCardStatus);
