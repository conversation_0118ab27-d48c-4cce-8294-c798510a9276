import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema, WeeklyWorkPlanSchema } from '@shape-construction/api/src/types';
import Button from '@shape-construction/arch-ui/src/Button';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import { CheckCircleIcon as OutlinedCheckCircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { PlusIcon, CheckCircleIcon as SolidCheckCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery, useModal } from '@shape-construction/hooks';
import useExports from 'app/components/Exports/useExports';
import { PublishLookbackConfirmationModal } from 'app/pages/projects/[projectId]/weekly-planner/plan/components/PublishLookbackConfirmationModal/PublishLookbackConfirmationModal';
import { PublishPlanConfirmationModal } from 'app/pages/projects/[projectId]/weekly-planner/plan/components/PublishPlanConfirmationModal/PublishPlanConfirmationModal';
import { useProjectPerson } from 'app/queries/projects/people';
import {
  useArchiveWeeklyWorkPlan,
  usePublishLookbackWeeklyWorkPlan,
  usePublishWeeklyWorkPlan,
  useRestoreWeeklyWorkPlan,
} from 'app/queries/weeklyWorkPlans/weeklyWorkPlans';
import { renderMarkup } from 'react-render-markup';
import { ExportPlanLookbackButton } from './components/ExportPlanLookback/ExportPlanLookbackButton';
import type { ExportPlanOption } from './components/ExportPlanLookback/types';
import { DraftVisibilityMessage } from './components/PlanDetailLayoutSection/DraftVisibilityMessage';
import { Ppc } from './components/PlanDetailLayoutSection/Ppc';
import { PlanEditorDropdown } from './components/PlanEditorDropdown';
import { PlanSavingIndicator } from './components/PlanSavingIndicator/PlanSavingIndicator';
import { LOOKBACK_STATUSES, PLAN_STATUS } from './constants/constants';

type PlanLayoutActionsProps = {
  plan: WeeklyWorkPlanSchema;
  projectId: ProjectSchema['id'];
  openExportModal: (option: ExportPlanOption) => void;
  openPlanModal: () => void;
  openDuplicatePlanModal: () => void;
  showNewProgressLogButton: boolean;
  openDrawer: () => void;
};

export const PlanLayoutActions = ({
  plan,
  projectId,
  openExportModal,
  openPlanModal,
  openDuplicatePlanModal,
  showNewProgressLogButton,
  openDrawer,
}: PlanLayoutActionsProps) => {
  const isMediumScreen = useMediaQuery(breakpoints.up('md'));
  const isSmallScreen = useMediaQuery(breakpoints.up('sm'));
  const actionsMessages = useMessageGetter('weeklyPlanner.workPlans.table.actions');
  const successToastMessage = useMessageGetter('weeklyPlanner.workPlans.table');
  const messages = useMessageGetter('weeklyPlanner.workPlans.details');
  const { data: author } = useProjectPerson(projectId, plan?.teamMemberId!);
  const isLookback = LOOKBACK_STATUSES.some((status) => status === plan?.status);
  const isDraft = plan?.status === PLAN_STATUS.DRAFT;
  const publishLookbackMessages = useMessageGetter('weeklyPlanner.workPlans.publishLookback.modal');
  const publishLookbackModal = useModal(false);
  const { mutate: publishLookback, isPending: isPublishingLookback } = usePublishLookbackWeeklyWorkPlan();
  const { addToPendingExports } = useExports();
  const onPublishLookback = () => {
    publishLookback(
      { projectId, weeklyWorkPlanId: plan.id },
      {
        onSuccess: (data) => {
          if (data?.exportQueuedTask) {
            showSuccessToast({
              message: renderMarkup(publishLookbackMessages('successToast', { planTitle: plan.title })),
            });
            addToPendingExports(data.exportQueuedTask);
          }
          publishLookbackModal.closeModal();
        },
      }
    );
  };

  const publishPlanMessages = useMessageGetter('weeklyPlanner.workPlans.publishPlan.modal');
  const publishPlanModal = useModal(false);
  const { mutate: publishPlan, isPending: isPublishingPlan } = usePublishWeeklyWorkPlan();
  const onPublishPlan = () => {
    publishPlan(
      { projectId, weeklyWorkPlanId: plan.id },
      {
        onSuccess: () => {
          showSuccessToast({
            message: publishPlanMessages('successToast'),
          });
          publishPlanModal.closeModal();
        },
      }
    );
  };

  const { mutate: archiveWeeklyWorkPlan } = useArchiveWeeklyWorkPlan();
  const onArchive = () => {
    archiveWeeklyWorkPlan(
      {
        projectId: projectId,
        weeklyWorkPlanId: plan.id,
      },
      {
        onSuccess: () => {
          showSuccessToast({
            message: successToastMessage('archivePlan.successToast', {
              planName: plan.title,
            }),
          });
        },
      }
    );
  };

  const { mutate: restoreWeeklyWorkPlan } = useRestoreWeeklyWorkPlan();
  const onRestore = () => {
    restoreWeeklyWorkPlan(
      {
        projectId: projectId,
        weeklyWorkPlanId: plan.id,
      },
      {
        onSuccess: () => {
          showSuccessToast({
            message: successToastMessage('restorePlan.successToast', {
              planName: plan.title,
            }),
          });
        },
      }
    );
  };

  return (
    <div className="flex gap-x-2 relative">
      {author && (
        <>
          {isDraft && <PlanSavingIndicator />}
          {!isSmallScreen && !isMediumScreen && (
            <>
              {isLookback && <Ppc value={plan.ppc} />}
              {isDraft && <DraftVisibilityMessage />}
            </>
          )}
          <PlanEditorDropdown
            plan={plan}
            onEdit={openPlanModal}
            onDuplicate={openDuplicatePlanModal}
            onArchive={onArchive}
            onRestore={onRestore}
          />
          <ExportPlanLookbackButton plan={plan} onExport={openExportModal} />
          {showNewProgressLogButton && (
            <span className="ml-2">
              <Button
                fullWidth
                color="primary"
                size={isMediumScreen ? 'sm' : 'xs'}
                variant="contained"
                leadingIcon={PlusIcon}
                onClick={() => openDrawer()}
              >
                {messages('newProgressLogCTA')}
              </Button>
            </span>
          )}
        </>
      )}

      {plan.availableActions?.close && (
        <>
          <Tooltip.Root>
            <Tooltip.Trigger asChild>
              {isSmallScreen ? (
                <Button
                  size={isMediumScreen ? 'sm' : 'xs'}
                  variant="contained"
                  color="primary"
                  leadingIcon={OutlinedCheckCircleIcon}
                  onClick={publishLookbackModal.openModal}
                >
                  {actionsMessages('publishLookback')}
                </Button>
              ) : (
                <IconButton
                  size={isMediumScreen ? 'sm' : 'xs'}
                  variant="outlined"
                  color="primary"
                  icon={SolidCheckCircleIcon}
                  shape="square"
                  onClick={publishLookbackModal.openModal}
                />
              )}
            </Tooltip.Trigger>
            <Tooltip.Content>{actionsMessages('publishLookback')}</Tooltip.Content>
          </Tooltip.Root>

          <PublishLookbackConfirmationModal
            isOpen={publishLookbackModal.open}
            onClose={publishLookbackModal.closeModal}
            onConfirm={onPublishLookback}
            disabled={isPublishingLookback}
          />
        </>
      )}

      {plan.availableActions?.publish && (
        <>
          <Tooltip.Root>
            <Tooltip.Trigger asChild>
              {isSmallScreen ? (
                <Button
                  size={isMediumScreen ? 'sm' : 'xs'}
                  variant="contained"
                  color="primary"
                  leadingIcon={OutlinedCheckCircleIcon}
                  onClick={publishPlanModal.openModal}
                >
                  {actionsMessages('publishPlan')}
                </Button>
              ) : (
                <IconButton
                  size={isMediumScreen ? 'sm' : 'xs'}
                  variant="outlined"
                  color="primary"
                  icon={SolidCheckCircleIcon}
                  shape="square"
                  onClick={publishPlanModal.openModal}
                />
              )}
            </Tooltip.Trigger>
            <Tooltip.Content>{actionsMessages('publishPlan')}</Tooltip.Content>
          </Tooltip.Root>

          <PublishPlanConfirmationModal
            isOpen={publishPlanModal.open}
            onClose={publishPlanModal.closeModal}
            onConfirm={onPublishPlan}
            disabled={isPublishingPlan}
          />
        </>
      )}
    </div>
  );
};
