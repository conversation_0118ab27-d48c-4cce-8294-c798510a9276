import React from 'react';
import { useLayout } from '@shape-construction/arch-ui/src/hooks/useLayout';
import { ArrowsPointingOutIcon, Bars4Icon } from '@shape-construction/arch-ui/src/Icons/solid';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useMediaQuery } from '@shape-construction/hooks';
import { removeQueryParams } from 'app/lib/utils/query-string';
import { useLocation, useMatch, useNavigate } from 'react-router';
import { SplitViewIcon } from './components/SplitViewIcon';

const classes = {
  section: 'flex flex-row items-center bg-gray-200 rounded-md outline-solid outline-gray-200 gap-x-0.5',
  button: {
    default: 'p-2 rounded-md hover:bg-white',
    selected: 'bg-white outline-solid outline-gray-300',
  },
  icon: 'h-5 w-5 text-gray-600',
};

export const NavigationActions = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const isOnNotifications = useMatch('/notifications/projects/:projectId');
  const { expandedPanel, setExpandedPanel } = useLayout();
  const isLargeScreen = useMediaQuery(breakpoints.up('lg'));

  return (
    <div role="menu" aria-label="navigation actions" className={classes.section}>
      <button
        type="button"
        aria-label="expand issues list"
        data-testid="expand-issues-list"
        className={cn(classes.button.default, {
          [classes.button.selected]: expandedPanel === 0,
        })}
        onClick={() => {
          if (isOnNotifications) return navigate('/notifications', { replace: true });

          return navigate(
            {
              search: removeQueryParams(location.search, ['issueId']),
            },
            {
              replace: true,
            }
          );
        }}
      >
        <Bars4Icon className={classes.icon} />
      </button>
      {isLargeScreen && (
        <button
          aria-label="split panels"
          type="button"
          className={cn(classes.button.default, {
            [classes.button.selected]: !expandedPanel,
          })}
          onClick={() => setExpandedPanel(undefined)}
        >
          <SplitViewIcon className={classes.icon} />
        </button>
      )}
      <button
        type="button"
        aria-label="expand issue details"
        className={cn(classes.button.default, {
          [classes.button.selected]: expandedPanel === 1,
        })}
        onClick={() => setExpandedPanel(1)}
      >
        <ArrowsPointingOutIcon className={classes.icon} />
      </button>
    </div>
  );
};
