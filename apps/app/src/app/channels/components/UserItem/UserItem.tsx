import React from 'react';
import { Avatar as DefaultAvatar } from '@shape-construction/arch-ui/src/Avatar/Avatar';
import './UserItem.css';
import { AtSymbolIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';

export type UserItemProps = {
  /** The user */
  entity: {
    /** The parts of the Name property of the entity (or id if no name) that can be matched to the user input value.
     * Default is bold for matches, but can be overwritten in css.
     * */
    itemNameParts?: { match: string; parts: string[] };
    /** Id of the user */
    id?: string;
    /** Image of the user */
    image?: string;
    /** Name of the user */
    name?: string;
    /** Email of the user */
    email?: string;
  };
  /** Custom UI component to display user avatar, defaults to and accepts same props as: [Avatar](https://github.com/GetStream/stream-chat-react/blob/master/src/components/Avatar/Avatar.tsx) */
  Avatar?: typeof DefaultAvatar;
};

/**
 * UI component for mentions rendered in suggestion list
 */
const UnMemoizedUserItem: React.FC<UserItemProps> = ({ Avatar = DefaultAvatar, entity }) => {
  const hasEntity = !!Object.keys(entity).length;
  const itemParts = entity?.itemNameParts;

  const renderName = () => {
    if (!hasEntity) return null;

    return itemParts?.parts.map((part, i) => {
      const matches = part.toLowerCase() === itemParts.match.toLowerCase();
      const partKey = i;
      return (
        <span
          className={cn(
            {
              'str-chat__emoji-item--highlight': matches,
              'str-chat__emoji-item--part': !matches,
            },
            'truncate text-sm font-medium text-neutral-bold'
          )}
          key={`part-${partKey}`}
        >
          {part}
        </span>
      );
    });
  };

  return (
    <div className="str-chat__user-item">
      <Avatar size="md" imgURL={entity.image} text={entity.name || entity.id || ''} />
      <span className="str-chat__user-item--name" data-testid={'user-item-name'}>
        {renderName()}
        {entity.email && <div className="truncate text-xs text-neutral-subtle">{entity.email}</div>}
      </span>
      <div className="str-chat__user-item-at">
        <AtSymbolIcon />
      </div>
    </div>
  );
};

export const UserItem = React.memo(UnMemoizedUserItem) as typeof UnMemoizedUserItem;
