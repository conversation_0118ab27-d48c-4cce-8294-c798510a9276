// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { NotificationParametersShiftReportCommentMentionSchema } from '../types/notificationParametersShiftReportCommentMentionSchema';
import { createNotificationActor } from './createNotificationActor';
import { faker } from '@faker-js/faker';

export function createNotificationParametersShiftReportCommentMention(
  data?: Partial<NotificationParametersShiftReportCommentMentionSchema>
): NotificationParametersShiftReportCommentMentionSchema {
  faker.seed([100]);
  return {
    ...{
      type: faker.string.alpha(),
      actor: createNotificationActor(),
      params: {
        channel: faker.helpers.arrayElement<
          NonNullable<NonNullable<NotificationParametersShiftReportCommentMentionSchema>['params']>['channel']
        >(['public', 'collaborators']),
        commentId: faker.string.uuid(),
        projectId: faker.string.uuid(),
        shiftReportDate: faker.date.anytime().toISOString().substring(0, 10),
        shiftReportId: faker.string.uuid(),
        shiftReportTitle: faker.string.alpha(),
      },
    },
    ...(data || {}),
  };
}
