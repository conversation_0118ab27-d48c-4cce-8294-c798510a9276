import * as BottomSheet from '@shape-construction/arch-ui-native/src/BottomSheet';
import { useBottomSheetContext } from '@shape-construction/arch-ui-native/src/BottomSheet';
import {
  ChatBubbleBottomCenterTextIcon,
  DocumentPlusIcon,
  PlusIcon,
} from '@shape-construction/arch-ui-native/src/Icons/solid';
import { Link } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { Text, TouchableOpacity, View } from 'react-native';

export const CreateChannelBottomSheet = () => {
  const { t } = useTranslation();
  const { bottomSheetModalRef } = useBottomSheetContext();
  const handleDismissBottomSheet = () => bottomSheetModalRef.current?.dismiss();

  return (
    <>
      <View className="flex items-center justify-center">
        <BottomSheet.Trigger asChild>
          <TouchableOpacity
            accessibilityRole="button"
            aria-label={t('create.title')}
            className="flex items-center justify-center h-16 w-16 bg-brand-bold rounded-full"
          >
            <PlusIcon className="h-9 w-9 text-white" />
          </TouchableOpacity>
        </BottomSheet.Trigger>
      </View>
      <BottomSheet.Modal>
        <BottomSheet.Content className="pt-4 px-0">
          <Link href="/channel/new" asChild>
            <TouchableOpacity
              accessibilityRole="link"
              onPress={handleDismissBottomSheet}
              className="flex flex-row items-center p-4 border-b border-t border-neutral-subtlest"
            >
              <ChatBubbleBottomCenterTextIcon className="w-6 h-6 text-icon-neutral-subtle mr-3" />
              <Text className="text-sm leading-5 font-medium text-neutral">{t('create.channel.title')}</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/notes/new" asChild>
            <TouchableOpacity
              accessibilityRole="link"
              onPress={handleDismissBottomSheet}
              className="flex flex-col items-start p-4 border-b border-neutral-subtlest"
            >
              <View className="flex flex-row items-center">
                <DocumentPlusIcon className="w-6 h-6 text-icon-neutral-subtle mr-3" />
                <Text className="text-sm leading-5 font-medium text-neutral">{t('create.note.title')}</Text>
              </View>
              <Text className="text-xs leading-4 font-normal text-neutral-subtle">{t('create.note.description')}</Text>
            </TouchableOpacity>
          </Link>
        </BottomSheet.Content>
      </BottomSheet.Modal>
    </>
  );
};
