import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ShiftReportSchema } from '@shape-construction/api/src/types';
import Table from '@shape-construction/arch-ui/src/Table';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { FormSectionHeader } from '../components/FormSection';
import { ResourcesDocumentsPreview } from './components/ResourcesDocumentsPreview';
import { ShiftDowntimeIssuePreview } from './components/ShiftDowntimeIssuePreview';

type ShiftReportDowntimeProps = {
  shiftReport: ShiftReportSchema;
};

export const ShiftReportDowntime = ({
  shiftReport: { downTimes, projectId, id: shiftReportId },
}: ShiftReportDowntimeProps) => {
  const messages = useMessageGetter('shiftReport.form');

  return (
    <fieldset className="grid grid-cols-1 gap-6">
      <legend>
        <FormSectionHeader className="mb-4">{messages('downTime')}</FormSectionHeader>
      </legend>

      <div
        className="overflow-auto rounded-md shadow ring-1 ring-black/5
         [&_div]:overflow-visible [&_div]:shadow-none [&_div]:ring-0"
      >
        <Table.Container>
          <Table>
            <Table.Heading>
              <Table.Row className="[&_th]:pl-3 [&_th]:normal-case">
                <Table.Header>{messages('issueDescription')}</Table.Header>
                <Table.Header>{messages('timeLost')}</Table.Header>
                <Table.Header>{messages('reason')}</Table.Header>
              </Table.Row>
            </Table.Heading>
            <Table.Body className="[&_tr]:last:border-b-0 [&_td]:text-gray-800">
              {downTimes.length ? (
                downTimes.map((downTime) => {
                  const { id, issueDescription, timeLost, causalType, issueId } = downTime;

                  return (
                    <React.Fragment key={id}>
                      <Table.Row
                        className={cn({
                          'border-b': !issueId,
                          'border-b-0': issueId,
                        })}
                      >
                        <Table.Cell className="whitespace-pre-line">{issueDescription}</Table.Cell>
                        <Table.Cell>{timeLost}</Table.Cell>
                        <Table.Cell>{causalType}</Table.Cell>
                      </Table.Row>
                      {!!downTime.documentCount && (
                        <Table.Row>
                          <Table.Cell colSpan={3} width={100} className="pt-0 [&_div]:overflow-x-auto">
                            <ResourcesDocumentsPreview
                              projectId={projectId}
                              shiftReportId={shiftReportId}
                              resource={downTime}
                              resourceType="down_times"
                            />
                          </Table.Cell>
                        </Table.Row>
                      )}
                      <ShiftDowntimeIssuePreview issueId={issueId} projectId={projectId!} />
                    </React.Fragment>
                  );
                })
              ) : (
                <Table.Row>
                  <Table.Cell colSpan={3}>
                    <span className="text-gray-400">{messages('noEntries')}</span>
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table>
        </Table.Container>
      </div>
    </fieldset>
  );
};
