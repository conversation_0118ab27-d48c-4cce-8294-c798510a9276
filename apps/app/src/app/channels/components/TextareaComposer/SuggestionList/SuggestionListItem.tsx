import { forwardRef, type Ref, useCallback, useLayoutEffect, useRef } from 'react';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { type SuggestionItemProps, useMessageComposer } from 'stream-chat-react';
import { UserItem } from '../../UserItem';

export const SuggestionListItem = forwardRef<HTMLButtonElement, SuggestionItemProps>(function SuggestionListItem(
  { className, focused, item, onMouseEnter }: SuggestionItemProps,
  innerRef: Ref<HTMLButtonElement>
) {
  const { textComposer } = useMessageComposer();
  const containerRef = useRef<HTMLLIElement>(null);

  const handleSelect = useCallback(() => {
    textComposer.handleSelect(item);
  }, [item, textComposer]);

  useLayoutEffect(() => {
    if (!focused) return;
    containerRef.current?.scrollIntoView({ behavior: 'instant', block: 'nearest' });
  }, [focused, containerRef]);

  return (
    <li
      className={cn('str-chat__suggestion-list-item', className, {
        'str-chat__suggestion-item--selected': focused,
      })}
      onMouseEnter={onMouseEnter}
      ref={containerRef}
    >
      <button
        type="button"
        className="text-left w-full"
        onClick={handleSelect}
        onKeyDown={(event) => {
          if (event.key === 'Enter') {
            handleSelect();
          }
        }}
        ref={innerRef}
      >
        <UserItem entity={item} />
      </button>
    </li>
  );
});
