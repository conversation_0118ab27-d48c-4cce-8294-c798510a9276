import React, { useEffect, useState } from 'react';
import type { IssueSchema, ProjectSchema } from '@shape-construction/api/src/types';
import SplitLayout from '@shape-construction/arch-ui/src/SplitLayout';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useMediaQuery } from '@shape-construction/hooks';
import { STATUS_CODES } from 'app/constants/StatusCodes';
import { LayoutConfigs } from 'app/contexts/layout/layoutConfigs';
import { useLayoutContext } from 'app/contexts/layout/layoutContext';
import { NotAuthorised } from 'app/pages/projects/[projectId]/components/NotAuthorised';
import { NotFound } from 'app/pages/projects/[projectId]/issues/components/NotFound';
import { useProjectIssue } from 'app/queries/issues/issues';
import { useProject } from 'app/queries/projects/projects';
import { useLocation, useParams } from 'react-router';
import { CrashPage } from '../CrashPage/CrashPage';
import { LoadingSpinner } from '../Loading/Loading';

const splitLayout = {
  split: undefined,
  list: 0,
  details: 1,
};

type Params = {
  projectId: ProjectSchema['id'];
};

export interface SplittedIssueLayoutProps {
  listComponent: (props: { projectId: ProjectSchema['id'] }) => React.ReactNode;
  detailsComponent: (props: { issue: IssueSchema }) => React.ReactNode;
}

export const SplittedIssueLayout: React.FC<SplittedIssueLayoutProps> = ({ listComponent, detailsComponent }) => {
  const { setLayoutConfig } = useLayoutContext();
  const searchParams = new URLSearchParams(useLocation().search);
  const issueId = searchParams.get('issueId');
  const { projectId } = useParams() as Params;
  const isMediumScreen = useMediaQuery(breakpoints.up('md'));
  const isLargeScreen = useMediaQuery(breakpoints.up('lg'));
  const { data: project } = useProject(projectId);
  const { data: issue, error, isError, isLoading } = useProjectIssue(projectId, issueId!);
  const validIssue = project && issue && issue.projectId === projectId;

  const [expandedPanelIndex, setExpandedPanelIndex] = useState(validIssue ? splitLayout.split : splitLayout.list);

  const isDisplayingIssueDetailsOnSmallScreen = !isMediumScreen && expandedPanelIndex === splitLayout.details;

  // If there's any issue selected, it will set split-screen (undefined option)
  useEffect(() => {
    if (!project) {
      // Show error screen with no split
      setExpandedPanelIndex(splitLayout.list);
    } else {
      setExpandedPanelIndex(issueId ? splitLayout.split : splitLayout.list);
    }
  }, [project, issueId]);

  // Expand issue details panel when is split-screen and <1024px screen
  useEffect(() => {
    if (!Number.isInteger(expandedPanelIndex) && !isLargeScreen) setExpandedPanelIndex(splitLayout.details);
  }, [isLargeScreen, expandedPanelIndex]);

  useEffect(() => {
    if (!isDisplayingIssueDetailsOnSmallScreen) return;

    setLayoutConfig(LayoutConfigs.hideNavVariant);
  }, [isDisplayingIssueDetailsOnSmallScreen, setLayoutConfig]);

  useEffect(() => {
    if (isDisplayingIssueDetailsOnSmallScreen) return;

    setLayoutConfig(LayoutConfigs.projectListVariant);
  }, [isDisplayingIssueDetailsOnSmallScreen, setLayoutConfig]);

  const isFromCurrentProject = !!project && !issue;

  const renderFallbackScreen = () => {
    switch (error?.response?.status) {
      case STATUS_CODES.FORBIDDEN:
        return <NotAuthorised isFromCurrentProject={isFromCurrentProject} />;
      case STATUS_CODES.NOT_FOUND:
        return <NotFound />;
      default:
        return <CrashPage />;
    }
  };

  const renderIssuePanel = () => {
    if (isLoading)
      return (
        <div className="flex h-full justify-center">
          <LoadingSpinner />
        </div>
      );
    if (isError) return renderFallbackScreen();
    if (!issue) return null;

    return detailsComponent({ issue });
  };

  return (
    <SplitLayout
      onSetLayout={setExpandedPanelIndex}
      expandedPanel={expandedPanelIndex}
      className="flex-1 md:overflow-hidden"
    >
      <SplitLayout.Panel
        className={cn({
          // apply width to issues list panel only when layout is on split mode
          'w-96 lg:w-5/12': expandedPanelIndex === splitLayout.split,
        })}
      >
        {/* When issueId is present, do not render the event list on a small screen */}
        {/* to avoid flickering when loading the associated issue details. */}
        {issueId && !isMediumScreen ? null : listComponent({ projectId })}
      </SplitLayout.Panel>

      <SplitLayout.Panel className="flex-1 overflow-hidden">{renderIssuePanel()}</SplitLayout.Panel>
    </SplitLayout>
  );
};
