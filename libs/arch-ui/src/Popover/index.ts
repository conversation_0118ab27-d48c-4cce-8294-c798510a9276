import {
  PopoverContent,
  Popover<PERSON>ontentBody,
  PopoverContentFooter,
  Popover<PERSON>ontentHeading,
  PopoverRoot,
  PopoverTrigger,
} from './Popover';

export default Object.assign(PopoverRoot, {
  Root: PopoverRoot,
  Content: Object.assign(PopoverContent, {
    Heading: PopoverContentHeading,
    Body: PopoverContentBody,
    Footer: PopoverContentFooter,
  }),
  Trigger: PopoverTrigger,
});
