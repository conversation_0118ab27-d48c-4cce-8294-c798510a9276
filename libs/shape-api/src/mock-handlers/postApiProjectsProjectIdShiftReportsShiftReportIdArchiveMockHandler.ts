/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftReportsShiftReportIdArchiveMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftReportsShiftReportIdArchiveSchema';
import { createPostApiProjectsProjectIdShiftReportsShiftReportIdArchiveMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftReportsShiftReportIdArchive';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftReportsShiftReportIdArchiveMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdArchiveMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('/api/projects/:project_id/shift_reports/:shift_report_id/archive', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdShiftReportsShiftReportIdArchiveMutationResponse(data)),
      {
        status: 200,
      }
    );
  });
}
