// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueEventParametersUpdateSchema } from '../types/issueEventParametersUpdateSchema';
import { createIssueEventUpdateDateTimeChangeParameters } from './createIssueEventUpdateDateTimeChangeParameters';
import { createIssueEventUpdateStringChangeParameters } from './createIssueEventUpdateStringChangeParameters';
import { createIssueImpact } from './createIssueImpact';
import { faker } from '@faker-js/faker';

export function createIssueEventParametersUpdate(
  data?: Partial<IssueEventParametersUpdateSchema>
): IssueEventParametersUpdateSchema {
  faker.seed([100]);
  return {
    ...{
      eventType: faker.string.alpha(),
      parameters: {
        changes: {
          category: {
            to: faker.helpers.arrayElement<
              NonNullable<
                NonNullable<
                  NonNullable<NonNullable<IssueEventParametersUpdateSchema>['parameters']>['changes']
                >['category']
              >['to']
            >(['close out', 'something needed', 'progress', 'safety']),
            from: faker.helpers.arrayElement<
              NonNullable<
                NonNullable<
                  NonNullable<NonNullable<IssueEventParametersUpdateSchema>['parameters']>['changes']
                >['category']
              >['from']
            >(['close out', 'something needed', 'progress', 'safety']),
          },
          closedAt: createIssueEventUpdateDateTimeChangeParameters(),
          critical: { to: faker.datatype.boolean(), from: faker.datatype.boolean() },
          delayFinish: createIssueEventUpdateDateTimeChangeParameters(),
          delayStart: createIssueEventUpdateDateTimeChangeParameters(),
          description: createIssueEventUpdateStringChangeParameters(),
          discipline: createIssueEventUpdateStringChangeParameters(),
          dueDate: createIssueEventUpdateDateTimeChangeParameters(),
          immediateAction: createIssueEventUpdateStringChangeParameters(),
          impact: { to: Object.assign({}, createIssueImpact()), from: Object.assign({}, createIssueImpact()) },
          location: createIssueEventUpdateStringChangeParameters(),
          observedAt: createIssueEventUpdateDateTimeChangeParameters(),
          peopleInvolvedSafety: createIssueEventUpdateStringChangeParameters(),
          plannedClosureDate: createIssueEventUpdateDateTimeChangeParameters(),
          potentialImpactSeverity: createIssueEventUpdateStringChangeParameters(),
          preventativeAction: createIssueEventUpdateStringChangeParameters(),
          safetyAlert: { to: faker.datatype.boolean(), from: faker.datatype.boolean() },
          safetyLikelihoodScore: { to: faker.number.int(), from: faker.number.int() },
          subCategory: createIssueEventUpdateStringChangeParameters(),
          title: createIssueEventUpdateStringChangeParameters(),
          visibilityStatus: {
            to: Object.assign({}, createIssueImpact()),
            from: Object.assign({}, createIssueImpact()),
          },
          workAffected: createIssueEventUpdateStringChangeParameters(),
        },
      },
    },
    ...(data || {}),
  };
}
