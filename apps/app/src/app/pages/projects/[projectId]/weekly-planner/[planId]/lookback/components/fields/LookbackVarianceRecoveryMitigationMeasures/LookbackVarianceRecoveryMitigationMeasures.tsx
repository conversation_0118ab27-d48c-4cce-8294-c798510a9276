import React from 'react';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { AutoResizeTextarea } from 'app/pages/projects/[projectId]/weekly-planner/plan/components/AutoResizeTextarea/AutoResizeTextarea';
import type { LookbackFieldProps } from '../types';

type LookbackVarianceRecoveryMitigationMeasuresProps = LookbackFieldProps;

export const LookbackVarianceRecoveryMitigationMeasures: React.FC<LookbackVarianceRecoveryMitigationMeasuresProps> = ({
  planActivity,
  onUpdateActivity,
  isEditable,
}) => {
  if (!isEditable) {
    return <span>{planActivity.varianceRecoveryMitigationMeasures || ''}</span>;
  }

  return (
    <AutoResizeTextarea
      rows={1}
      minHeight={36}
      key={planActivity.varianceRecoveryMitigationMeasures}
      name="varianceRecoveryMitigationMeasures"
      value={planActivity.varianceRecoveryMitigationMeasures || undefined}
      className={cn(
        'border shadow-none border-transparent hover:border-gray-300 focus:border-gray-300 min-w-[224px] -mt-1 font-normal',
        'bg-transparent focus:bg-white hover:bg-white'
      )}
      onBlur={(e: React.FocusEvent<HTMLTextAreaElement>) => {
        onUpdateActivity({ variance_recovery_mitigation_measures: e.target.value });
      }}
    />
  );
};
