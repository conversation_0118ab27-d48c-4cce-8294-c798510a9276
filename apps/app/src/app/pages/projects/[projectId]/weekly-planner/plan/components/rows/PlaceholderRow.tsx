import React from 'react';
import { useMessage } from '@messageformat/react';
import Table from '@shape-construction/arch-ui/src/Table';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useSearchFieldAutofocus } from '../../hooks/useSearchFieldAutofocus';
import { allColumns, useColumnOptions } from '../componentTypes';
import type { PlaceholderComponentProps } from '../fields/types';

type PlaceholderRowProps = PlaceholderComponentProps & {
  shouldOffsetCell: boolean;
};

export const PlaceholderRow = ({ plan, shouldOffsetCell }: PlaceholderRowProps) => {
  const placeholderFormLabel = useMessage('weeklyPlanner.workPlans.planEditor.placeholderFormLabel');
  const { focusSearchField } = useSearchFieldAutofocus();

  const { columnOptions } = useColumnOptions();

  return (
    <Table.Row
      striped
      className="border-b border-b-gray-300 last:border-b-0 hover:bg-gray-100 group/activity"
      onClick={focusSearchField}
      aria-label={placeholderFormLabel}
    >
      {shouldOffsetCell && <Table.Cell /> /* Additional column for drag handle */}

      {allColumns.map((field) => {
        const PlaceholderComponent = columnOptions[field].placeholderComponent;
        return (
          <Table.Cell
            key={`placeholder/${field}`}
            className={cn(
              'px-2 py-1 sm:first:pl-2 sm:last:pr-2',
              'leading-5 border-r border-r-gray-300 last:border-r-0',
              'text-sm font-normal text-gray-800'
            )}
          >
            {PlaceholderComponent && <PlaceholderComponent plan={plan} />}
          </Table.Cell>
        );
      })}
    </Table.Row>
  );
};
