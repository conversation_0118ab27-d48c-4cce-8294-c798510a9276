import React, { useCallback } from 'react';
import { useMessageGetter } from '@messageformat/react';
import AvatarGroupStacked from '@shape-construction/arch-ui/src/Avatar/AvatarGroupStacked';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import { TrashIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import type { User } from '@shape-construction/arch-ui/src/types/User';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import type { UseFieldArrayRemove } from 'react-hook-form';

export type FormRowWithActionsProps = {
  children: React.ReactNode;
  rowIndex: number;
  user?: User;
  onDelete: UseFieldArrayRemove;
  deleteIcon?: React.ElementType;
};

export const FormRowWithActions: React.FC<FormRowWithActionsProps> = ({
  children,
  rowIndex,
  user,
  onDelete,
  deleteIcon: DeleteIcon,
}) => {
  const messages = useMessageGetter('shiftReport.form');
  const getHighlightColor = useCallback(() => 'neutral', []);

  const handleDeleteItem = useCallback(
    (event: React.MouseEvent<HTMLButtonElement>, index: number) => {
      event.preventDefault();
      onDelete(index);
    },
    [onDelete]
  );

  return (
    <div className="flex flex-end gap-4" data-cy="shift-report-row">
      <div className="w-full">{children}</div>
      <div className={cn('flex items-end justify-end', { 'min-w-[64px]': Boolean(user) })}>
        {user && (
          <div className="py-2 h-10 grow flex justify-center">
            <AvatarGroupStacked size="sm" getHighlightColor={getHighlightColor} users={[user]} />
          </div>
        )}
        <IconButton
          aria-label={messages('delete')}
          color="secondary"
          size="md"
          variant="text"
          icon={DeleteIcon || TrashIcon}
          onClick={(event) => {
            handleDeleteItem(event, rowIndex);
          }}
        />
      </div>
    </div>
  );
};
