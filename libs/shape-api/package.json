{"name": "@shape-construction/api", "version": "1.0.0", "description": "", "engines": {"node": "v22.18.0", "pnpm": "10.15.0"}, "scripts": {"compile": "tsc -p tsconfig.json", "schema:update": "./scripts/update_api_schema", "schema:generate": "kubb generate && pnpm run format", "format": "biome format --write", "generate": "pnpm run schema:update && pnpm run schema:generate"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@biomejs/biome": "2.0.6", "@kubb/cli": "3.18.3", "@kubb/core": "3.18.3", "@kubb/plugin-faker": "3.18.3", "@kubb/plugin-msw": "3.18.3", "@kubb/plugin-oas": "3.18.3", "@kubb/plugin-react-query": "3.18.3", "@kubb/plugin-ts": "3.18.3", "@kubb/react": "3.18.3", "typescript": "5.8.3"}, "dependencies": {"@faker-js/faker": "9.9.0", "@shape-construction/utils": "workspace:*", "@tanstack/react-query": "5.52.1", "axios": "1.10.0", "stream-chat": "9.6.0"}}