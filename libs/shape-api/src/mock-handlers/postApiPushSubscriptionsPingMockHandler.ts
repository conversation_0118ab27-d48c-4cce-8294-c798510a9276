/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiPushSubscriptionsPingMutationResponseSchema } from '../types/postApiPushSubscriptionsPingSchema';
import { createPostApiPushSubscriptionsPingMutationResponse } from '../factories/createPostApiPushSubscriptionsPing';
import { http } from 'msw';

export function postApiPushSubscriptionsPingMockHandler(
  data?:
    | PostApiPushSubscriptionsPingMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('/api/push_subscriptions/ping', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(JSON.stringify(data || createPostApiPushSubscriptionsPingMutationResponse(data)), {
      status: 204,
    });
  });
}
