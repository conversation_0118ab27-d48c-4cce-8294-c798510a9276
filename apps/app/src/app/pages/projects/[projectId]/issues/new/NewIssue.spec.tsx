import React from 'react';
import { issueFactory } from '@shape-construction/api/factories/issues';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { teamFactory } from '@shape-construction/api/factories/teams';
import { userBasicDetailsFactory, userFactory } from '@shape-construction/api/factories/users';
import { getApiProjectsProjectIdIssuesIssueIdMockHandler } from '@shape-construction/api/handlers-factories/projects/issues';
import { getApiProjectsProjectIdPeopleMockHandler } from '@shape-construction/api/handlers-factories/projects/team-members';
import { now } from '@shape-construction/utils/DateTime';
import { useConstructionRoles } from 'app/components/People/constructionRoles/hooks/useConstructionRoles';
import { createReduxStore } from 'app/store';
import { createMemoryHistory } from 'history';
import { constructionRoleFactory } from 'tests/factories/constructionRoles';
import { server } from 'tests/mock-server';
import { stateFactory } from 'tests/state-factory';
import { render, screen, userEvent, waitFor } from 'tests/test-utils';
import { NewIssue } from './NewIssue';
import { LAST_STEP_IDX } from './Steps';

jest.mock('messages');
jest.mock('app/components/People/constructionRoles/hooks/useConstructionRoles');

const mockUseConstructionRoles = (props = {}) => {
  (useConstructionRoles as jest.Mock).mockImplementation(() => ({
    isLoading: false,
    constructionRoles: constructionRoleFactory(),
    ...props,
  }));
};

describe('<NewIssue />', () => {
  beforeEach(() => {
    mockUseConstructionRoles();
    jest.spyOn(window.navigator, 'onLine', 'get').mockReturnValue(true);
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('when offline', () => {
    it('shows an offline message', async () => {
      jest.spyOn(window.navigator, 'onLine', 'get').mockReturnValue(false);

      render(<NewIssue />);

      expect(await screen.findByText('offline.title')).toBeInTheDocument();
    });

    it('allows to close dialog', async () => {
      jest.spyOn(window.navigator, 'onLine', 'get').mockReturnValue(false);
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-one/issues', '/projects/project-one/issues/new'],
        initialIndex: 1,
      });
      const route = {
        path: '/projects/:projectId/issues/new',
      };
      render(<NewIssue />, { history, route });
      expect(await screen.findByRole('dialog')).toBeInTheDocument();

      await userEvent.click(await screen.findByRole('button', { name: 'Close Overlay' }));

      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      await waitFor(() => expect(history.location.pathname).toEqual('/projects/project-one/issues'));
    });

    it('selects current user by default', async () => {
      const userBasicDetails = userBasicDetailsFactory({ name: 'Current Person' });
      const currentUser = userFactory(userBasicDetails);
      const currentTeamMember = teamMemberFactory({
        team: teamFactory({ displayName: 'Persons Team' }),
        user: userBasicDetails,
      });
      server.use(getApiProjectsProjectIdPeopleMockHandler(() => [currentTeamMember, teamMemberFactory()]));
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-one/issues/new'],
      });
      const route = {
        path: '/projects/:projectId/issues/new',
      };

      render(<NewIssue initialStep={LAST_STEP_IDX} />, {
        history,
        route,
        user: currentUser,
      });

      await waitFor(() => expect(screen.getByRole('radio', { name: /Current Person/i })).toBeChecked());
    });
  });

  describe('when opening existing draft', () => {
    it('prefills available data', async () => {
      const existingDraft = issueFactory({ currentState: 'draft' });
      server.use(getApiProjectsProjectIdIssuesIssueIdMockHandler(() => existingDraft));
      const initialState = stateFactory()({
        builderIssueId: existingDraft.id,
      });
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-one/issues/new'],
      });
      const route = {
        path: '/projects/:projectId/issues/new',
      };

      render(<NewIssue />, { initialState, history, route });

      await waitFor(() =>
        expect(screen.getByRole('textbox', { name: 'issue.new.titleAndDescription.labels.title' })).toHaveValue(
          existingDraft.title
        )
      );
    });
  });

  it('clears builder issue id when unmounting', async () => {
    const initialState = stateFactory()({
      builderIssueId: 'draft-id',
    });
    const { store } = createReduxStore(initialState);

    const { unmount } = render(<NewIssue />, { store });
    unmount();

    expect(store.getState().issues.builderIssueId).toBe(null);
  });

  describe('Due date step', () => {
    it('allows inserting due dates in the past', async () => {
      const dueDateValue = () => now().subtract(7, 'day');
      const dueDateFormatted = dueDateValue().format('YYYY-MM-DD');
      const messages = {
        issue: {
          detail: { impact: { dates: { info: ({ timezone }: any) => `Timezone: ${timezone}` } } },
        },
      };
      const route = { path: '/projects/:projectId/issues/new' };
      const history = createMemoryHistory({ initialEntries: ['/projects/project-one/issues/new'] });
      render(<NewIssue initialStep={1} />, { messages, route, history });

      expect(await screen.findByText('issue.new.dueDate.description')).toBeInTheDocument();
      await waitFor(() => expect(screen.getByRole('button', { name: 'issue.new.controls.nextCTA' })).toBeDisabled());

      await userEvent.type(await screen.findByLabelText('issue.new.dueDate.labels.dueDate'), dueDateFormatted);

      expect(await screen.findByLabelText('issue.new.dueDate.labels.dueDate')).toHaveValue(dueDateFormatted);
      expect(screen.getByRole('button', { name: 'issue.new.controls.nextCTA' })).toBeEnabled();
    });
  });

  describe('when submitting on the last step', () => {
    it('replaces the current location with review page', async () => {
      const existingDraft = issueFactory({
        currentState: 'draft',
        id: 'draft-id',
        projectId: 'project-one',
      });
      server.use(
        getApiProjectsProjectIdIssuesIssueIdMockHandler(() => existingDraft),
        getApiProjectsProjectIdPeopleMockHandler(() => [teamMemberFactory()])
      );
      const initialState = stateFactory()({
        builderIssueId: 'draft-id',
      });
      const history = createMemoryHistory({ initialEntries: ['/projects/project-one/issues/new'] });
      render(<NewIssue initialStep={LAST_STEP_IDX} />, {
        initialState,
        history,
        route: {
          path: '/projects/:projectId/issues/new',
        },
      });

      await userEvent.click(await screen.findByRole('radio'));

      await waitFor(() => {
        expect(history.location.pathname).toEqual('/projects/project-one/issues/lists/unpublished');
      });
      expect(history.entries.length).toBe(1);
    });
  });

  describe('closing the modal', () => {
    describe('if there is a background location', () => {
      it('redirects to background location', async () => {
        const initialState = stateFactory()();
        const history = createMemoryHistory({
          initialEntries: [],
        });
        history.push('/projects/project-one/issues/new', {
          background: {
            pathname: '/projects/project-one/timeline',
          },
        });
        render(<NewIssue />, {
          initialState,
          history,
          route: {
            path: '/projects/:projectId/issues/new',
          },
        });

        await userEvent.type(
          await screen.findByRole('textbox', {
            name: 'issue.new.titleAndDescription.labels.title',
          }),
          'a new draft'
        );

        await userEvent.click(await screen.findByRole('button', { name: 'Close Overlay' }));

        await waitFor(() => expect(history.location.pathname).toBe('/projects/project-one/timeline'));
      });
    });
  });
});
