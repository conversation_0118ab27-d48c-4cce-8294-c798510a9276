import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ShiftActivityOverviewResourcesUsageEntrySchema } from '@shape-construction/api/src/types';
import Button from '@shape-construction/arch-ui/src/Button';
import { InformationCircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { ArrowsPointingOutIcon, IdentificationIcon, UsersIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { parseDate } from '@shape-construction/utils/DateTime';
import {
  useActivityInsightsResourcesUsage,
  useActivityInsightsResourcesUsageStats,
} from 'app/queries/activities/activities';
import { type Params, useNavigate, useParams } from 'react-router';
import { IconText } from './components/IconText';
import { Widget } from './components/Widget';
import { WidgetTable } from './components/WidgetTable';
import { trimWidgetEntryList } from './utils';

type ResourcesUsedWidgetProps = {
  expanded?: boolean;
};

export const ResourcesUsedWidget: React.FC<ResourcesUsedWidgetProps> = ({ expanded }) => {
  const { projectId, shiftActivityId } = useParams<Params>() as Params;
  const messages = useMessageGetter('shiftManager.activities.resourcesUsed');
  const { data, isLoading, isError } = useActivityInsightsResourcesUsage(projectId!, shiftActivityId!);
  const { data: stats } = useActivityInsightsResourcesUsageStats(projectId!, shiftActivityId!);
  const navigate = useNavigate();

  const { equipmentTotalHoursUsage, personnelTotalHoursWorked } = stats || {};

  const columns = [
    {
      key: 'description',
      label: messages('tableHeaders.material'),
    },
    {
      key: 'quantity',
      label: messages('tableHeaders.quantity'),
    },
    {
      key: 'units',
      label: messages('tableHeaders.unit'),
    },
    {
      key: 'reportDate',
      label: messages('tableHeaders.date'),
      render: (row: ShiftActivityOverviewResourcesUsageEntrySchema) =>
        `${parseDate(row.reportDate).format('DD-MMM-YYYY')}`,
    },
  ];

  return (
    <Widget
      actions={
        !expanded && (
          <Button
            aria-label={messages('actions.seeAll')}
            size="xxs"
            color="secondary"
            variant="outlined"
            leadingIcon={ArrowsPointingOutIcon}
            onClick={() => {
              navigate(`/projects/${projectId}/activities/${shiftActivityId}/resources-used`);
            }}
          >
            {messages('actions.seeAll')}
          </Button>
        )
      }
      title={messages('title')}
      isLoading={isLoading}
      isError={isError}
    >
      <div className="flex flex-row flex-wrap gap-2">
        <IconText
          icon={<UsersIcon width={18} height={18} />}
          label={<span id="personnelTotalHours">{messages('personnelTotalHours.label')}</span>}
          data={
            <Tooltip.Root>
              <Tooltip.Trigger className="flex flex-row align-center gap-2" aria-labelledby="personnelTotalHours">
                {personnelTotalHoursWorked}
                <InformationCircleIcon width={18} height={18} className="text-icon-selected-bold" />
              </Tooltip.Trigger>
              <Tooltip.Content side="bottom">{messages('personnelTotalHours.tooltip')}</Tooltip.Content>
            </Tooltip.Root>
          }
        />
        <IconText
          icon={<IdentificationIcon width={18} height={18} />}
          label={<span id="equipmentTotalHours">{messages('equipmentTotalHours.label')}</span>}
          data={
            <Tooltip.Root>
              <Tooltip.Trigger className="flex flex-row align-center gap-2" aria-labelledby="equipmentTotalHours">
                {equipmentTotalHoursUsage}
                <InformationCircleIcon width={18} height={18} className="text-icon-selected-bold" />
              </Tooltip.Trigger>
              <Tooltip.Content side="bottom">{messages('equipmentTotalHours.tooltip')}</Tooltip.Content>
            </Tooltip.Root>
          }
        />
      </div>
      <WidgetTable<ShiftActivityOverviewResourcesUsageEntrySchema>
        data={
          (expanded
            ? data?.entries
            : trimWidgetEntryList(data?.entries)) as ShiftActivityOverviewResourcesUsageEntrySchema[]
        }
        columns={columns}
        emptyStateLabels={{
          title: messages('emptyStateLabels.title'),
          subtitle: messages('emptyStateLabels.subtitle'),
        }}
      />
    </Widget>
  );
};
