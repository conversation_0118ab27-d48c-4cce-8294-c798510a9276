import React, { useEffect, useMemo } from 'react';
import { useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import Modal from '@shape-construction/arch-ui/src/ModalBase';
import { showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks/src/useMediaQuery';
import { FilterForm } from 'app/components/Filters/FilterForm/FilterForm';
import { useFiltersStateParams } from 'app/components/Filters/hooks/useFiltersParams';
import { useCurrentProject } from 'app/contexts/currentProject';
import { useUpdateShiftActivitiesFinderOptions } from 'app/queries/weeklyWorkPlans/weeklyWorkPlans';
import { renderMarkup } from 'react-render-markup';
import { useActivitiesList } from './ActivitiesList/useActivitiesList';
import type { FilterFormValues } from './ActivitiesToolbar/types';
import { useInitialValues } from './ActivitiesToolbar/useInitialValues';
import { Content } from './Content';
import { formatOptionsToBody } from './utils';

type AddActivitiesModalProps = {
  onClose: () => void;
  onSelectActivities: (ids: string[]) => Promise<unknown>;
  isSelecting?: boolean;
};

export const AddActivitiesModal: React.FC<AddActivitiesModalProps> = ({ onClose, onSelectActivities, isSelecting }) => {
  const project = useCurrentProject();
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const messages = useMessageGetter('weeklyPlanner.workPlans.planEditor.addActivitiesModal');

  const { shiftActivitiesData } = useActivitiesList();
  const { mutate: updateActivitiesOptions } = useUpdateShiftActivitiesFinderOptions();

  const { initialValues, isFetchingInitialValues } = useInitialValues();
  const { filters, updateParams } = useFiltersStateParams(initialValues);

  const values: FilterFormValues = useMemo(
    () => ({
      assigned_team_member_id: filters.assigned_team_member_id,
      location_id: filters.location_id,
      organisation_resource_id: filters.organisation_resource_id,
      owner_id: filters.owner_id,
      planned_start_date: filters.planned_start_date,
      planned_end_date: filters.planned_end_date,
      actual_start_date: filters.actual_start_date,
      expected_finish_date: filters.expected_finish_date,
      status: filters.status,
      critical: filters.critical,
      search: filters.search,
      selected_ids: filters.selected_ids,
      multi_select: filters.multi_select,
      weekly_work_plan_id: filters.weekly_work_plan_id,
    }),
    [filters]
  );

  const saveOptions = () => {
    updateActivitiesOptions({
      projectId: project.id,
      data: formatOptionsToBody(values),
    });
  };

  const handleSubmit = (values: FilterFormValues) => {
    updateParams(values);
  };

  const handleCloseModal = () => {
    saveOptions();
    updateParams({
      ...values,
      selected_ids: [],
      multi_select: 'none',
    });
    onClose();
  };
  const selectedIds = filters.selected_ids;
  const enableAddSelectedCTA = selectedIds && selectedIds.length > 0;

  const selectActivities = async () => {
    await onSelectActivities(selectedIds);

    handleCloseModal();
    showSuccessToast({
      message: messages('successToast', { numberOfActivities: selectedIds?.length }),
      alignContent: 'start',
    });
  };

  useEffect(() => {
    if (!shiftActivitiesData || !selectedIds) return;

    const activityIds = shiftActivitiesData.map((activity) => activity.id);
    const filteredSelectedIds = selectedIds.filter((id) => activityIds.includes(id));

    if (
      filteredSelectedIds.length !== selectedIds.length ||
      !filteredSelectedIds.every((id) => filters.selected_ids?.includes(id))
    ) {
      updateParams({ ...filters, selected_ids: filteredSelectedIds });
    }
  }, [shiftActivitiesData, selectedIds, updateParams]);

  const disableAddSelectedCTA = isFetchingInitialValues || !enableAddSelectedCTA || isSelecting;

  return (
    <FilterForm
      values={values}
      defaultValues={initialValues}
      onSubmit={handleSubmit}
      autoSubmitFields={{
        large: ['search', 'selected_ids', 'multi_select', 'weekly_work_plan_id'],
        small: ['search', 'selected_ids', 'multi_select', 'weekly_work_plan_id'],
      }}
    >
      <Modal.Root
        open
        onClose={handleCloseModal}
        transition={isLargeScreen ? 'fade' : 'bottom-to-top'}
        roundBorders={isLargeScreen}
        outsidePad={isLargeScreen}
        fullScreen={!isLargeScreen}
        className="w-screen md:min-h-full"
      >
        <Modal.Header onClose={handleCloseModal}>
          <Modal.Title>{messages('title')}</Modal.Title>
        </Modal.Header>
        <Modal.Content className="p-0 bg-neutral-subtle gap-2">
          <Content isFetchingInitialValues={isFetchingInitialValues} handleSubmit={handleSubmit} />
        </Modal.Content>
        <Modal.Footer>
          <Button color="secondary" variant="outlined" size="md" onClick={handleCloseModal}>
            {messages('cancelCTA')}
          </Button>
          <Tooltip.Root>
            <Tooltip.Trigger asChild>
              <Button
                color="primary"
                variant="contained"
                size="md"
                onClick={selectActivities}
                disabled={disableAddSelectedCTA}
                aria-disabled={disableAddSelectedCTA}
              >
                {messages('addSelectedCTA')}
              </Button>
            </Tooltip.Trigger>
            <Tooltip.Content side="top" hidden={!disableAddSelectedCTA}>
              {renderMarkup(messages('tooltip'))}
            </Tooltip.Content>
          </Tooltip.Root>
        </Modal.Footer>
      </Modal.Root>
    </FilterForm>
  );
};
