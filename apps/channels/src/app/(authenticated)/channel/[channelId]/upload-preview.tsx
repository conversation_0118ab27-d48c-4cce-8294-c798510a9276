import { type ComponentProps, useCallback, useEffect, useRef, useState } from 'react';
import { useActionSheet } from '@expo/react-native-action-sheet';
import { cn } from '@shape-construction/arch-ui-native';
import { PhotoIcon, TrashIcon } from '@shape-construction/arch-ui-native/src/Icons/outline';
import { PaperAirplaneIcon, XMarkIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { useNavigation, useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useTranslation } from 'react-i18next';
import { FlatList, type ListRenderItem, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { DocumentPreview } from 'src/components/AttachmentPicker/components/DocumentPreview/DocumentPreview';
import { DocumentThumbnail } from 'src/components/AttachmentPicker/components/DocumentThumbnail/DocumentThumbnail';
import { useAttachmentPickerActions } from 'src/components/AttachmentPicker/hooks/useAttachmentPickerActions';
import { useCustomChannelTitle } from 'src/components/Channel/hooks/useCustomChannelTitle';
import LocationPickerBottomSheet from 'src/components/LocationPickerBottomSheet/LocationPickerBottomSheet';
import type { Node } from 'src/components/NodeList/types';
import { AutoCompleteInput, type FileUpload, useChannelContext, useMessageInputContext } from 'stream-chat-expo';

export const UploadPreviewScreen = () => {
  const router = useRouter();
  const { t } = useTranslation();
  const navigation = useNavigation();
  const scrollRef = useRef<FlatList>(null);
  const { channel } = useChannelContext();
  const { imageUploads, fileUploads, sendMessage, resetInput, removeFile, removeImage, text } =
    useMessageInputContext();
  const { showActionSheetWithOptions } = useActionSheet();
  const { bottom: bottomInset } = useSafeAreaInsets();
  const { camera, gallery, documents } = useAttachmentPickerActions();
  const { channelTitle } = useCustomChannelTitle(channel);
  const [selectedMediaIndex, setSelectedMediaIndex] = useState(0);
  const [selectedLocation, setSelectedLocation] = useState<Node>();

  const media = [...imageUploads, ...fileUploads];
  const selectedMedia = media.at(selectedMediaIndex);
  const onSendMessage = async () => {
    await sendMessage({
      customMessageData: {
        text,
        attachments_caption: text,
        attachments_location_id: selectedLocation?.id,
      },
    });
    router.dismissTo({
      pathname: '/channel/[channelId]',
      params: { channelId: channel.cid },
    });
  };

  const onReturnPage = useCallback(() => {
    resetInput();
    router.back();
  }, []);

  const toggleMediaAttachment = () => {
    showActionSheetWithOptions(
      {
        cancelButtonIndex: 3,
        containerStyle: { paddingBottom: bottomInset },
        destructiveButtonIndex: 3,
        options: [
          t('channel.mediaOptions.photos'),
          t('channel.mediaOptions.camera'),
          t('channel.mediaOptions.documents'),
          t('actions.cancel'),
        ],
      },
      async (buttonIndex) => {
        if (Number.isInteger(buttonIndex)) {
          const actions = [gallery, camera, documents];
          actions[buttonIndex!]();
        }
      }
    );
  };

  const renderItem: ListRenderItem<FileUpload> = useCallback(
    ({ item, index }) => {
      const isSelected = selectedMedia?.id === item.id;
      const buttonProps: ComponentProps<typeof TouchableOpacity> = {
        'aria-selected': isSelected,
        'aria-label': isSelected ? 'remove media' : 'media',
        onPress: () => {
          if (isSelected) {
            const isImage = item.file.type === 'image';
            isImage ? removeImage(item.id) : removeFile(item.id);
            setSelectedMediaIndex(index === 0 ? 0 : index - 1);
          } else {
            scrollRef.current?.scrollToIndex({ index });
            setSelectedMediaIndex(index);
          }
        },
      };

      return (
        <TouchableOpacity
          key={item.id}
          accessibilityRole="button"
          className={cn('relative flex items-center justify-center h-20 w-20 bg-neutral overflow-hidden', {
            'border border-neutral-inverse': isSelected,
          })}
          {...buttonProps}
        >
          <DocumentThumbnail document={item} />
          {isSelected && (
            <>
              <View className="h-full w-full absolute bg-overlay-subtle" />
              <TrashIcon className="absolute inset-0 text-white h-8 w-8" />
            </>
          )}
        </TouchableOpacity>
      );
    },
    [selectedMedia]
  );

  // Side effect responsible for resetting the input when the user navigates back
  useEffect(() => {
    navigation.addListener('beforeRemove', () => {
      resetInput();
    });
  }, []);

  const isShowLocationPicker = channel.data?.type === 'group' || channel.data?.type === 'team';

  return (
    <LocationPickerBottomSheet.Root onSelectLocation={setSelectedLocation} selectedLocation={selectedLocation}>
      <StatusBar style="light" />
      <SafeAreaView edges={['top', 'bottom']} className="flex flex-col flex-1 bg-black">
        <View className="flex-1 relative">
          <View className="flex flex-row w-full justify-between items-center px-2 bg-transparent">
            <TouchableOpacity
              accessibilityRole="button"
              aria-label="close"
              className="mt-4 h-10 w-10 p-3 rounded-full z-10 bg-neutral-alpha-bold flex items-center justify-center"
              onPress={onReturnPage}
            >
              <XMarkIcon className="text-white h-5 w-5" />
            </TouchableOpacity>
          </View>

          {selectedMedia && (
            <View className="flex-1">
              <DocumentPreview document={selectedMedia} />
            </View>
          )}

          <View className="absolute w-full bottom-0 mb-4 flex flex-col gap-1 justify-center bg-transparent">
            {media.length > 1 && (
              <FlatList<FileUpload>
                data={media}
                id="_id"
                initialNumToRender={10}
                onScrollToIndexFailed={() => {
                  scrollRef.current?.scrollToIndex({ index: 0 });
                }}
                keyExtractor={(item) => item.id}
                renderItem={renderItem}
                ref={scrollRef}
                horizontal
                contentContainerStyle={{ flexGrow: 1, justifyContent: 'center', gap: 8 }}
              />
            )}
          </View>
        </View>

        <View className="flex flex-col bg-neutral-alpha-bold gap-2 py-4">
          {isShowLocationPicker && <LocationPickerBottomSheet.Trigger />}
          <View className="py-1.5 px-3 mx-2 flex flex-row justify-between items-center gap-3 rounded-3xl border border-neutral-bold bg-surface-inverse">
            <TouchableOpacity
              accessibilityRole="button"
              aria-label="attachment options"
              onPress={toggleMediaAttachment}
              className="self-start"
            >
              <PhotoIcon className="mt-0.5 android:mt-1.5 w-6 h-6 text-icon-neutral-inverse" />
            </TouchableOpacity>

            <AutoCompleteInput
              cooldownActive
              numberOfLines={3}
              additionalTextInputProps={{
                'aria-label': 'caption',
                placeholder: 'Add a caption...',
                className: 'flex-1 py-1 text-base leading-5 font-normal max-h-[100] text-white',
                style: {},
              }}
            />
          </View>
          <View className="mx-2 flex flex-row items-center justify-between">
            <Text className="text-base leading-none font-normal text-accent-yellow-inverse">{channelTitle}</Text>
            <TouchableOpacity
              accessibilityRole="button"
              aria-label="send message"
              className="p-3 rounded-full bg-brand-bold"
              onPress={onSendMessage}
            >
              <PaperAirplaneIcon className="text-white h-4 w-4" />
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
      <LocationPickerBottomSheet.Panel />
    </LocationPickerBottomSheet.Root>
  );
};

export default UploadPreviewScreen;
