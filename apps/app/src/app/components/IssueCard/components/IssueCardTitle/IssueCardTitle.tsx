import React from 'react';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';

type IssueCardTitleProps = {
  className?: string;
  content: string;
};

const IssueCardTitle = ({ className, content }: IssueCardTitleProps) => (
  <span
    data-cy="issue-card-title"
    className={cn('overflow-hidden text-ellipsis whitespace-nowrap text-base font-medium leading-6', className)}
  >
    {content}
  </span>
);

export default React.memo(IssueCardTitle);
