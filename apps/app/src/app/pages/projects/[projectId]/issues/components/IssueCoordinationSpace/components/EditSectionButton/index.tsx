import React, { type ComponentProps } from 'react';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';

export type EditSectionButtonProps = ComponentProps<'button'>;

const EditSectionButton: React.FC<EditSectionButtonProps> = ({ children, title, onClick, ...props }) => (
  <button
    type="button"
    onClick={onClick}
    className={cn('w-full rounded-md p-2 text-left', {
      'select-text': props.disabled,
      'hover:bg-gray-100': !props.disabled,
    })}
    {...props}
  >
    {title && <h3 className="mb-1 text-sm font-medium tracking-wide text-gray-500">{title}</h3>}
    {children}
  </button>
);

export default EditSectionButton;
