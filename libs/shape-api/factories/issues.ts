import { faker } from '@faker-js/faker';
import type {
  IssueApproverSchema,
  IssueAssignmentSchema,
  IssueCustomFieldSchema,
  IssueDetailsExtraSchema,
  IssueSchema,
  IssueStatusStatementSchema,
  IssueSummarySchema,
} from '../src/types';
import { teamMemberFactory } from './team-member';
import { teamFactory } from './teams';
import { userBasicDetailsFactory } from './users';
import type { Factory } from './utils';

export const customFieldsFactory: Factory<IssueCustomFieldSchema> = (props) => ({
  customFieldId: faker.string.uuid(),
  label: 'Label of custom field',
  projectWide: false,
  value: 'Content of custom field',
  ...props,
});

export const issueFactory = (issueProperties: Partial<IssueSchema> = {}): IssueSchema => {
  const now = new Date().toISOString();

  return {
    activityLevel: 'active',
    approvers: [],
    archived: false,
    assignedTeamId: null,
    assignedTeamMemberId: null,
    assignedTeamName: null,
    assignedUser: userBasicDetailsFactory({
      id: faker.string.uuid(),
      name: 'Current Assigned User',
    }),
    availableActions: issueAvailableActionsFactory(),
    category: 'progress',
    createdAt: now,
    critical: false,
    currentState: 'assignment_requested',
    customFields: [],
    delayFinish: null,
    delayStart: null,
    description: 'issue description',
    disciplineId: null,
    draftAssigneeId: null,
    dueDate: now,
    id: faker.string.uuid(),
    immediateAction: null,
    impact: null,
    involvedTeams: [],
    isWatching: false,
    issueAssignment: issueAssignmentsFactory(),
    issueStatusStatement: null,
    locationId: faker.string.uuid(),
    nextActionerId: 0,
    nextActionerUser: userBasicDetailsFactory(),
    observerId: faker.number.int(),
    observerTeamId: faker.string.uuid(),
    observerUser: userBasicDetailsFactory({
      id: faker.string.uuid(),
      name: 'Observed By User',
    }),
    originatorId: faker.number.int(),
    overdue: false,
    peopleInvolvedSafety: null,
    plannedClosureDate: null,
    potentialImpactSeverity: null,
    preventativeAction: null,
    projectId: faker.string.uuid(),
    qualityScore: null,
    referenceNumber: '',
    safetyAlert: false,
    safetyLikelihoodScore: null,
    subCategory: null,
    title: 'My issue',
    updates: {
      lastVisitedAt: now,
      unreadUpdatesCount: issueUnreadUpdatesCountFactory(),
    },
    updatedAt: now,
    visibilityStatus: 'project_wide',
    workAffected: null,
    ...issueProperties,
  };
};

export const issueSummaryFactory = (issueSummaryProperties: Partial<IssueSummarySchema> = {}): IssueSummarySchema => ({
  archived: false,
  currentState: 'assignment_requested',
  description: 'issue description',
  id: faker.string.uuid(),
  observerId: 1,
  qualityScore: null,
  referenceNumber: null,
  title: 'My issue',
  ...issueSummaryProperties,
});

export const issueAvailableActionsFactory = (
  issueAvailableActionsProperties: Partial<IssueDetailsExtraSchema['availableActions']> = {}
): IssueDetailsExtraSchema['availableActions'] => ({
  acceptAssignment: false,
  approve: false,
  archive: false,
  assign: false,
  complete: false,
  edit: true,
  reject: false,
  reopen: false,
  restore: false,
  start: false,
  stop: false,
  upload: false,
  ...issueAvailableActionsProperties,
});

export const issueAssignmentsFactory = (
  issueAssignmentsProperties: Partial<IssueAssignmentSchema> = {}
): IssueAssignmentSchema => ({
  id: faker.string.uuid(),
  assignee: teamMemberFactory(),
  assigneeId: faker.string.uuid() as unknown as number,
  assigner: teamMemberFactory(),
  assignerId: faker.string.uuid() as unknown as number,
  status: 'pending',
  rejectReason: null,
  canRespondTo: false,
  ...issueAssignmentsProperties,
});

export const issueApproverFactory = (issueApproverProperties: Partial<IssueApproverSchema>): IssueApproverSchema => ({
  id: faker.string.uuid(),
  teamMemberId: faker.number.int(),
  sortOrder: 1,
  approvedAt: null,
  status: 'pending',
  user: userBasicDetailsFactory({
    id: faker.string.uuid(),
    name: 'Approver Joe',
  }),
  team: teamFactory({
    displayName: 'Approver Team',
  }),
  ...issueApproverProperties,
});

export const issueUnreadUpdatesCountFactory = (
  issueUnreadUpdatesCountProperties: Partial<IssueSchema['updates']['unreadUpdatesCount']> = {}
): IssueSchema['updates']['unreadUpdatesCount'] => ({
  public: 0,
  team: 0,
  ...issueUnreadUpdatesCountProperties,
});

export const issueStatusStatementFactory = (issueStatusStatement?: Partial<IssueStatusStatementSchema>) => ({
  id: faker.string.uuid(),
  statement: 'This is a statement',
  date: '2021-09-01T00:00:00Z',
  availableActions: issueStatusStatementAvailableActionsFactory(),
  teamMemberId: 1,
  ...issueStatusStatement,
});

export const issueStatusStatementAvailableActionsFactory = (
  issueStatusStatementAvailableActions?: Partial<IssueStatusStatementSchema['availableActions']>
) => ({ delete: true, gracePeriodUntil: null, ...issueStatusStatementAvailableActions });
