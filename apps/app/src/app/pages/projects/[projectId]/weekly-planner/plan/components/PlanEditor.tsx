import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema, WeeklyWorkPlanSchema } from '@shape-construction/api/src/types';
import Button from '@shape-construction/arch-ui/src/Button';
import DraggableList from '@shape-construction/arch-ui/src/DraggableList';
import { PlusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import Table from '@shape-construction/arch-ui/src/Table';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { PLAN_STATUS } from 'app/pages/projects/[projectId]/weekly-planner/plan/constants/constants';
import { useSortedPlanActivities } from 'app/pages/projects/[projectId]/weekly-planner/plan/hooks/useSortedPlanActivities';
import { useSearchFieldAutofocus } from '../hooks/useSearchFieldAutofocus';
import { useWeeklyPlanFilters } from '../hooks/useWeeklyPlanFilters';
import { DraggableActivities } from './Activities/DraggableActivities';
import { GroupedActivities } from './Activities/GroupedActivities';
import { allColumns, useColumnOptions } from './componentTypes';
import { PlaceholderRow } from './rows/PlaceholderRow';

export type PlanEditorProps = {
  plan: WeeklyWorkPlanSchema;
  projectId: ProjectSchema['id'];
};

export const PlanEditor: React.FC<PlanEditorProps> = ({ plan, projectId }) => {
  const messages = useMessageGetter('weeklyPlanner.workPlans.planEditor');

  const {
    planActivities,
    isLoading: isLoadingActivities,
    movePlanActivity,
  } = useSortedPlanActivities(projectId, plan.id);
  const { focusSearchField } = useSearchFieldAutofocus();
  const { columnOptions } = useColumnOptions(plan);
  const {
    filters: { group },
  } = useWeeklyPlanFilters();
  const isDraft = plan.status === PLAN_STATUS.DRAFT;
  const hasGrouping = group !== 'none';
  const shouldOffsetCell = plan.availableActions.edit && group === 'none';

  if (isLoadingActivities) return null;

  return (
    <div className="overflow-x-auto h-full flex-1">
      <DraggableList.Root onDrop={movePlanActivity}>
        <Table className="table-fixed">
          <Table.Heading>
            <Table.Row className="bg-gray-200">
              {plan.availableActions.edit && group === 'none' && (
                <Table.Header className="sm:pl-2" /> /* Additional column for drag handle */
              )}
              {allColumns.map((field) => {
                const Header = columnOptions[field].header || Table.Header;
                return (
                  <Header
                    key={field}
                    className={cn(
                      'pl-2 pr-2 py-1 sm:last:pr-2',
                      'border-r border-r-gray-300 last:border-r-0',
                      'normal-case text-gray-500 font-bold truncate'
                    )}
                  >
                    {columnOptions[field].title}
                  </Header>
                );
              })}
            </Table.Row>
          </Table.Heading>
          <Table.Body className="bg-white text-gray-800">
            {hasGrouping ? (
              <GroupedActivities plan={plan} activities={planActivities} groupBy={group} projectId={projectId} />
            ) : (
              <DraggableActivities projectId={projectId} plan={plan} items={planActivities} />
            )}
            {isDraft && <PlaceholderRow plan={plan} shouldOffsetCell={shouldOffsetCell} />}
          </Table.Body>
          {isDraft && (
            <tfoot>
              <Table.Row key="add-activity" className="bg-gray-50 min-h-11">
                <Table.Cell colSpan={100} className="p-1 sm:first:pl-1 sm:last:pr-0">
                  <Button
                    aria-label={messages('addRowCTA')}
                    leadingIcon={PlusIcon}
                    color="primary"
                    size="md"
                    variant="text"
                    onClick={focusSearchField}
                  >
                    {messages('addRowCTA')}
                  </Button>
                </Table.Cell>
              </Table.Row>
            </tfoot>
          )}
        </Table>
      </DraggableList.Root>
    </div>
  );
};
