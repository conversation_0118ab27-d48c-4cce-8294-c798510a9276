import React, { useEffect, useMemo } from 'react';
import { useMessage, useMessageGetter } from '@messageformat/react';
import { PlusCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import Page from '@shape-construction/arch-ui/src/Page';
import { Preview } from 'app/channels/components/ChannelsPreview/Preview';
import { filterChannelsList, getProjectChannelsFilter } from 'app/channels/queries/useProjectChannels';
import { Link, Outlet, useOutlet, useParams } from 'react-router';
import type { Channel, ChannelSort } from 'stream-chat';
import { ChannelList, type ChannelListProps } from 'stream-chat-react';
import 'app/channels/components/ChannelsPreview/ChannelsList.css';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useMediaQuery } from '@shape-construction/hooks';
import { useCurrentProject } from 'app/contexts/currentProject';
import { LayoutConfigs } from 'app/contexts/layout/layoutConfigs';
import { useLayoutContext } from 'app/contexts/layout/layoutContext';

const sort: ChannelSort = [{ pinned_at: -1 }, { last_message_at: -1 }, { updated_at: -1 }];

export type Params = {
  channelId?: string;
};

export const ChannelsList = () => {
  const { setLayoutConfig } = useLayoutContext();
  const outlet = useOutlet();
  const messages = useMessageGetter('channels.channelsList');

  const project = useCurrentProject();
  const projectStreamTeam = project?.channels.streamChatTeam;
  const { channelId } = useParams() as Params;

  const filters: ChannelListProps['filters'] = useMemo(() => {
    if (!projectStreamTeam) return undefined;
    return getProjectChannelsFilter(project.id, projectStreamTeam);
  }, [project.id, projectStreamTeam]);

  const isHideNavigations = useMediaQuery(breakpoints.down('md')) && channelId;

  // Note: filters does not handle messages received in real time
  const handleChannelRenderFilter = (channels: Array<Channel>) => {
    return filterChannelsList(channels, project.id);
  };

  useEffect(() => {
    if (isHideNavigations) {
      setLayoutConfig(LayoutConfigs.hideNavVariant);
      return;
    }

    setLayoutConfig(LayoutConfigs.initialVariant);
  }, [isHideNavigations, setLayoutConfig]);

  return (
    <Page className="w-full h-full overflow-hidden flex flex-row ">
      <div
        className={cn('w-full flex-1 md:max-w-96 flex flex-col border-r border-neutral-subtle', {
          'hidden md:flex': !!outlet,
        })}
      >
        <div className="flex flex-row items-center justify-between p-4 cursor-default select-none">
          <div className="text-base leading-6 font-bold text-brand">{messages('title')}</div>

          <Link aria-label={useMessage('channels.newChannel.action')} to={`/projects/${project.id}/channels/new`}>
            <PlusCircleIcon className="w-6 h-6 text-brand-subtle" />
          </Link>
        </div>
        <ChannelList
          sort={sort}
          filters={filters}
          Preview={Preview}
          channelRenderFilterFn={handleChannelRenderFilter}
        />
      </div>
      {outlet && (
        <div className="flex-1 max-h-full overflow-hidden">
          <Outlet />
        </div>
      )}
    </Page>
  );
};

export { ChannelsList as Component };
