import React, { useMemo } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityIdMutationRequestSchema } from '@shape-construction/api/src/types';
import * as Select from '@shape-construction/arch-ui/src/Select';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { getVarianceCategories } from '../../../../../utils';
import type { LookbackFieldProps } from '../types';

type LookbackVarianceCategoryProps = LookbackFieldProps;

export const LookbackVarianceCategory: React.FC<LookbackVarianceCategoryProps> = ({
  planActivity,
  onUpdateActivity,
  isEditable,
}) => {
  const selectedVarianceCategory = planActivity.varianceCategory;

  const optionsMessages = useMessageGetter('weeklyPlanner.workPlans.lookback.varianceCategory.options');
  const options = useMemo(() => getVarianceCategories(optionsMessages), [optionsMessages]);
  const delayCategoryName = useMemo(
    () => options.find((option) => option.value === selectedVarianceCategory)?.label,
    [selectedVarianceCategory, options]
  );

  const handleOnChange = (value: string) => {
    onUpdateActivity({
      variance_category:
        value as PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityIdMutationRequestSchema['variance_category'],
    });
  };

  if (!isEditable) {
    return <span>{delayCategoryName || ''}</span>;
  }

  return (
    <Select.Root value={selectedVarianceCategory} onChange={handleOnChange} className="text-sm mt-0.5">
      <Select.Trigger
        as="div"
        variant="plain"
        size="sm"
        className={cn(
          'group hover:text-gray-800 hover:ring-2 hover:ring-gray-400 focus:ring-2 focus:ring-indigo-500 active:ring-2 active:ring-indigo-500',
          'bg-transparent focus:bg-white hover:bg-white'
        )}
        showChevronOnHover
      >
        {delayCategoryName && <Select.Value className="text-sm" value={delayCategoryName} />}
      </Select.Trigger>
      <Select.ResponsivePanel className="md:min-w-[340px]">
        <Select.Options>
          {options.map((option) => (
            <Select.Option key={option.label} value={option.value}>
              <Select.OptionText>{option.label}</Select.OptionText>
            </Select.Option>
          ))}
        </Select.Options>
      </Select.ResponsivePanel>
    </Select.Root>
  );
};
