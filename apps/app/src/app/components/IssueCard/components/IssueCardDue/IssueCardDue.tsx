import React from 'react';
import { CalendarIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { formatDue } from '@shape-construction/utils/DateTime';

type IssueCardDueProps = {
  className?: string;
  dueDate: string | null;
  isOverdue: boolean;
  timezone: string;
};

const IssueCardDue = ({ className, dueDate, isOverdue, timezone }: IssueCardDueProps) => {
  const date = React.useMemo(() => formatDue(dueDate, timezone), [dueDate, timezone]);

  return (
    <div className={cn('flex items-center gap-1.5 overflow-hidden whitespace-nowrap', className)}>
      <CalendarIcon
        className={cn('h-4 w-4', {
          'text-gray-400': !isOverdue,
          'text-yellow-500': isOverdue,
        })}
      />
      <span className="text-xs font-medium leading-4 text-gray-500">{date}</span>
    </div>
  );
};

export default React.memo(IssueCardDue);
