import React from 'react';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { AutoResizeTextarea } from '../../AutoResizeTextarea/AutoResizeTextarea';
import type { FieldProps } from '../types';

type CommentProps = Omit<FieldProps, 'onUpdateShiftActivity'>;

export const Comment: React.FC<CommentProps> = ({ planActivity, onUpdateActivity, isEditable }) => {
  if (!isEditable) return <div className="py-2">{planActivity.comment}</div>;

  return (
    <AutoResizeTextarea
      rows={1}
      minHeight={36}
      name="comment"
      value={planActivity.comment || undefined}
      className={cn(
        'border shadow-none border-transparent hover:border-gray-300 focus:border-gray-300 min-w-[224px] -mt-1 font-normal',
        'bg-transparent focus:bg-white hover:bg-white'
      )}
      onBlur={(e: React.FocusEvent<HTMLTextAreaElement>) => {
        onUpdateActivity({ comment: e.target.value });
      }}
    />
  );
};
