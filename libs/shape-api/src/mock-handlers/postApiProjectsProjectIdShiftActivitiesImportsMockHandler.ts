/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PostApiProjectsProjectIdShiftActivitiesImportsMutationResponseSchema } from '../types/postApiProjectsProjectIdShiftActivitiesImportsSchema';
import { createPostApiProjectsProjectIdShiftActivitiesImportsMutationResponse } from '../factories/createPostApiProjectsProjectIdShiftActivitiesImports';
import { http } from 'msw';

export function postApiProjectsProjectIdShiftActivitiesImportsMockHandler(
  data?:
    | PostApiProjectsProjectIdShiftActivitiesImportsMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.post>[1]>[0]) => Response)
) {
  return http.post('/api/projects/:project_id/shift_activities/imports', function handler(info) {
    if (typeof data === 'function') return data(info);

    return new Response(
      JSON.stringify(data || createPostApiProjectsProjectIdShiftActivitiesImportsMutationResponse(data)),
      {
        status: 202,
      }
    );
  });
}
