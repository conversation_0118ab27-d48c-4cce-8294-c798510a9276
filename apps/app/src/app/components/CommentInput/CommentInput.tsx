import React from 'react';
import type { UserSchema } from '@shape-construction/api/src/types';
import { UserAvatar } from '@shape-construction/arch-ui/src/Avatar';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import { ChatBubbleLeftEllipsisIcon, LockClosedIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { PaperAirplaneIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { type Editor, EditorContent } from '@tiptap/react';
import type { Configuration } from 'app/lib/comm-channel/configuration';
import { UploadFileActions, type UploadFileActionsProps } from '../Feed/components/UploadFileActions';
import './CommentInput.css';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';

export type CommentInputProps = {
  editor: Editor | null;
  hintLabel?: string;
  isOpen: boolean;
  onSubmit: () => void;
  onUploadFiles?: UploadFileActionsProps['onUpload'];
  theme: Configuration['theme'];
  fileUploadOptions?: UploadFileActionsProps['fileUploadOptions'];
  user?: UserSchema;
};

export const CommentInput: React.FC<CommentInputProps> = ({
  editor,
  hintLabel,
  isOpen,
  onSubmit,
  onUploadFiles,
  fileUploadOptions,
  theme,
  user,
}) => {
  const trimmedEditorText = editor?.getText().trim() ?? '';
  const disableCommentInput = !editor || trimmedEditorText === '';

  const onSubmitHandler = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    onSubmit();
  };

  const HintIcon = isOpen ? ChatBubbleLeftEllipsisIcon : LockClosedIcon;

  return (
    <form onSubmit={onSubmitHandler}>
      <div className="flex items-start space-x-2 h-full">
        <div className="flex flex-row gap-3 items-center">
          {onUploadFiles && (
            <div
              className={cn('py-2 flex flex-row gap-3', {
                'text-indigo-600': theme === 'primary',
                'text-yellow-600': theme === 'warning',
              })}
            >
              <UploadFileActions fileUploadOptions={fileUploadOptions} onUpload={onUploadFiles} />
            </div>
          )}
          {user && <UserAvatar user={user} size="md" />}
        </div>
        <div className="flex flex-auto flex-col gap-y-2">
          {editor && <EditorContent editor={editor} />}
          {hintLabel && (
            <div className="flex items-center gap-x-1.5 text-sm font-medium text-gray-400">
              <HintIcon className="h-4 w-4" /> {hintLabel}
            </div>
          )}
        </div>
        <IconButton
          type="submit"
          icon={PaperAirplaneIcon}
          disabled={disableCommentInput}
          aria-label="submit comment"
          data-cy="submit-comment"
          color={theme}
          size="md"
          variant="contained"
        />
      </div>
    </form>
  );
};
