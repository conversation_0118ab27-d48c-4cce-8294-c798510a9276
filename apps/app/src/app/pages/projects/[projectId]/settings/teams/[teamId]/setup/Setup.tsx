import React, { useEffect, useState } from 'react';
import { useMessage, useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import Page from '@shape-construction/arch-ui/src/Page';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useSuspenseQuery } from '@tanstack/react-query';
import { useQuotaLimits } from 'app/components/SubscriptionPlanFeatures/QuotaLimits/useQuotaLimits';
import { LayoutConfigs } from 'app/contexts/layout/layoutConfigs';
import { useLayoutContext } from 'app/contexts/layout/layoutContext';
import { useProjectPeople } from 'app/queries/projects/people';
import { useProjectInvite } from 'app/queries/projects/projects';
import { getProjectTeamQueryOptions, useUpdateTeam } from 'app/queries/projects/teams';
import { useNavigate, useParams } from 'react-router';
import { DisplayNameField, type DisplayNameFieldProps } from '../../components/DisplayNameField/DisplayNameField';
import { PeopleList } from '../../components/PeopleList/PeopleList';
import { QuickAddUser, type QuickAddUserProps } from '../../components/QuickAddUser/QuickAddUser';

type Params = {
  projectId: string;
  teamId: string;
};

export const Setup = () => {
  const navigate = useNavigate();
  const { projectId, teamId } = useParams() as Params;
  const messages = useMessageGetter('admin.teams.setup');
  const backNavigationTitle = useMessage('navigation.backTo', { route: 'Organisations' });
  const [displayNameSet, setDisplayNameSet] = useState(false);
  const { data: team } = useSuspenseQuery(getProjectTeamQueryOptions(projectId, teamId));

  const updateTeam = useUpdateTeam();
  const { quotaLimits, isLoading } = useQuotaLimits('usersPerTeam', {
    teamId: team?.id,
    disabled: !team,
  });
  const { data: teamMembers } = useProjectPeople(
    projectId,
    { team_id: team?.id },
    { query: { enabled: Boolean(team) } }
  );

  const projectInvite = useProjectInvite();

  const { setLayoutConfig } = useLayoutContext();
  useEffect(() => {
    setLayoutConfig(LayoutConfigs.showSideDrawer);
  }, [setLayoutConfig]);

  const handleUpdate: DisplayNameFieldProps['onSubmit'] = (values) => {
    updateTeam.mutate(
      {
        projectId,
        teamId,
        data: { display_name: values.displayName },
      },
      {
        onSuccess: () => setDisplayNameSet(true),
      }
    );
  };

  const handleInviteUser: QuickAddUserProps['onInviteUser'] = (values) => {
    if (!team) return;
    projectInvite.mutate({
      projectId,
      teamId: team.id,
      data: {
        email: values.email,
        role: 'contributor',
      },
    });
  };

  const redirectToProject = () => navigate(`/projects/${projectId}`);
  const redirectToTeams = () => navigate(`/projects/${projectId}/settings/teams`);

  return (
    <Page data-cy="team-page-setup">
      <Page.Header
        title={messages('header')}
        backNavigationTitle={backNavigationTitle}
        hasBackNavigation
        onBackNavigation={redirectToTeams}
      />
      <Page.Body className="bg-white">
        <h6 className={cn('pb-2 text-xl font-medium', displayNameSet ? 'text-gray-500' : 'text-indigo-500')}>
          {messages('step1')}
        </h6>
        {displayNameSet ? (
          <span className="text-xl font-medium">{team?.displayName}</span>
        ) : (
          <div className="space-y-4">
            <span className="text-base">{messages('nameLabel')}</span>
            <DisplayNameField
              submitButtonTitle={messages('actions.setName')}
              defaultValue={team?.org?.name || ''}
              onSubmit={handleUpdate}
            />
          </div>
        )}

        <hr className="my-4" />

        <h6 className={cn('pb-2 text-xl font-medium', displayNameSet ? 'text-indigo-500' : 'text-gray-400')}>
          {messages('step2')}
        </h6>

        <div hidden={!displayNameSet} className="space-y-4 pb-2">
          <span className="text-base">{messages('emailLabel')}</span>
          <QuickAddUser
            onInviteUser={handleInviteUser}
            disabled={isLoading}
            teamSize={
              quotaLimits && {
                maximum: quotaLimits.maximum,
                current: teamMembers?.length || 0,
              }
            }
          />
          {teamMembers && <PeopleList people={teamMembers} />}
        </div>

        <div className="flex justify-end gap-2">
          <Button
            color="primary"
            variant="contained"
            size="md"
            onClick={redirectToProject}
            data-testid="res-submit-button"
            disabled={!displayNameSet}
          >
            {messages('actions.done')}
          </Button>
        </div>
      </Page.Body>
    </Page>
  );
};

export { Setup as Component };
