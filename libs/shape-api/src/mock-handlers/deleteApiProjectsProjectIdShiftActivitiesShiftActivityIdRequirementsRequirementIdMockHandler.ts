/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DeleteApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdMutationResponseSchema } from '../types/deleteApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdSchema';
import { createDeleteApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdMutationResponse } from '../factories/createDeleteApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementId';
import { http } from 'msw';

export function deleteApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdMockHandler(
  data?:
    | DeleteApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdMutationResponseSchema
    | ((info: Parameters<Parameters<typeof http.delete>[1]>[0]) => Response)
) {
  return http.delete(
    '/api/projects/:project_id/shift_activities/:shift_activity_id/requirements/:requirement_id',
    function handler(info) {
      if (typeof data === 'function') return data(info);

      return new Response(
        JSON.stringify(
          data ||
            createDeleteApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdMutationResponse(
              data
            )
        ),
        {
          status: 204,
        }
      );
    }
  );
}
