import React, { forwardRef, useEffect, useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema, ResourceKindSchema, ResourceSchema } from '@shape-construction/api/src/types';
import { InputAdornment } from '@shape-construction/arch-ui/src/InputAdornment';
import type { SelectRootProps } from '@shape-construction/arch-ui/src/Select';
import * as Select from '@shape-construction/arch-ui/src/Select';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useInfiniteQuery, useQuery } from '@tanstack/react-query';
import { LoadMoreButton } from 'app/components/LoadMoreButton/LoadMoreButton';
import { NoSearchResults } from 'app/components/Search/NoSearchResults';
import { SearchLoading } from 'app/components/Search/SearchLoading';
import { getProjectQueryOptions } from 'app/queries/projects/projects';
import { getResourcesInfiniteQueryOptions } from 'app/queries/resources/resources';
import { useParams } from 'react-router';

type Params = { projectId: ProjectSchema['id'] };
type ResourcesSelectProps = SelectRootProps<string[], true> & {
  searchPlaceholder: string;
  triggerLabel: string;
  filterByLabel: string;
  kind: ResourceKindSchema;
};

const initialValue: string[] = [];

export const ResourcesSelect = forwardRef<React.ElementRef<typeof Select.Root>, ResourcesSelectProps>(
  ({ value = initialValue, onChange, triggerLabel, searchPlaceholder, filterByLabel, kind }, ref) => {
    const { projectId } = useParams() as Params;
    const messages = useMessageGetter('filters.toolbar.resourcesSelectFilter');

    const [checked, setChecked] = useState(value);
    const [searchTerm, setSearchTerm] = useState<string>();
    const [selectedResources, setSelectedResources] = useState<ResourceSchema[]>([]);

    const { data: project } = useQuery(getProjectQueryOptions(projectId!));
    const {
      data: searchedResources,
      isLoading,
      hasNextPage,
      isFetchingNextPage,
      fetchNextPage,
    } = useInfiniteQuery({
      ...getResourcesInfiniteQueryOptions(projectId, project?.currentTeamId!, kind, { search: searchTerm }),
      select: (data) => data?.pages.flatMap(({ entries }) => entries),
    });

    const handleOnChange = (values: string[]) => {
      onChange?.(values.map(String));
    };

    const handleOnRemove = (resource: ResourceSchema) => {
      setChecked((state) => state.filter((resourceId) => resourceId !== String(resource.id)));
      handleOnChange(checked.filter((resourceId) => resourceId !== String(resource.id)));
    };

    const renderContent = () => {
      if (isLoading) {
        return (
          <Select.PanelSection className="flex-1 w-full flex flex-col justify-center">
            <SearchLoading />
          </Select.PanelSection>
        );
      }

      if (searchedResources?.length === 0) {
        return (
          <Select.PanelSection className="flex-1 w-full flex flex-col items-center justify-center">
            <NoSearchResults />
          </Select.PanelSection>
        );
      }

      return searchedResources?.map((resource) => (
        <Select.Option key={resource.id} value={resource.id} className="md:min-h-11">
          <span>{resource.name}</span>
        </Select.Option>
      ));
    };

    useEffect(() => {
      if (value) {
        setChecked(value);

        const selectedResources = value.flatMap(
          (resourceId) => searchedResources?.find((resource) => resourceId === String(resource.id)) ?? []
        );

        setSelectedResources(selectedResources);
      }
    }, [value]);

    return (
      <Select.Root
        ref={ref}
        multiple
        by={(a, b) => String(a) === String(b)}
        value={checked}
        onChange={handleOnChange}
        onClose={() => setSearchTerm('')}
      >
        <Select.Trigger
          as="div"
          variant="bordered"
          size="sm"
          startAdornment={<InputAdornment>{triggerLabel}</InputAdornment>}
        >
          <div className="text-right">
            <Select.MultipleValue value={value} condensed />
          </div>
        </Select.Trigger>

        <Select.ResponsivePanel className="md:min-w-[340px] md:h-[40vh] h-[80vh]">
          <Select.PanelSection className="px-4 py-5 md:hidden">
            <h1 className="text-base leading-6 font-medium">{filterByLabel}</h1>
          </Select.PanelSection>

          <Select.PanelSection className={cn('max-h-[60px] overflow-y-auto', checked.length === 0 && 'hidden')}>
            <Select.MultipleValue
              value={selectedResources}
              onRemove={(resource) => handleOnRemove(resource)}
              onRemoveAll={() => {
                setChecked([]);
                handleOnChange([]);
              }}
              displayValue={(resource) => resource.name}
            />
          </Select.PanelSection>

          <Select.PanelSection>
            <Select.Search
              placeholder={searchPlaceholder}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                const { value } = event.target;
                if (!value || value.length > 1) {
                  setSearchTerm(value);
                }
              }}
            />
          </Select.PanelSection>
          <Select.Options className="flex-1">
            {renderContent()}

            {hasNextPage && (
              <LoadMoreButton
                isLoading={isFetchingNextPage}
                onClick={fetchNextPage}
                buttonType={messages('organisationSuffix')}
              />
            )}
          </Select.Options>
        </Select.ResponsivePanel>
      </Select.Root>
    );
  }
);

ResourcesSelect.displayName = 'ResourcesSelect';
